import { type Metadata } from 'next'
import Link from 'next/link'

import { <PERSON><PERSON> } from '@/components/Button'
import { SelectField, TextField } from '@/components/Fields'
import { Logo } from '@/components/Logo'
import { SlimLayout } from '@/components/SlimLayout'
import {LogoRankRender} from "@/components/LogoRankRender";

export const metadata: Metadata = {
  title: 'Get Started Free - RankRender',
  description: 'Get started with RankRender for free. No credit card required. Full access to JavaScript SEO optimization features.',
}

export default function Register() {
  return (
    <SlimLayout>
      <div className="flex">
        <Link href="/" aria-label="Home">
          <LogoRankRender className="h-10 w-auto" />
        </Link>
      </div>
      <h2 className="mt-20 text-lg font-semibold text-gray-900">
        Get Started Free
      </h2>
      <p className="mt-2 text-sm text-gray-700">
        Free forever plan, no credit card required. Already registered?{' '}
        <Link
          href="/login"
          className="font-medium text-rankrender-600 hover:underline"
        >
          Sign in
        </Link>{' '}
        to your account.
      </p>
      <form
        action="#"
        className="mt-10 grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-2"
      >
        <TextField
          label="First name"
          name="first_name"
          type="text"
          autoComplete="given-name"
          required
        />
        <TextField
          label="Last name"
          name="last_name"
          type="text"
          autoComplete="family-name"
          required
        />
        <TextField
          className="col-span-full"
          label="Email address"
          name="email"
          type="email"
          autoComplete="email"
          required
        />
        <TextField
          className="col-span-full"
          label="Password"
          name="password"
          type="password"
          autoComplete="new-password"
          required
        />
        <TextField
          className="col-span-full"
          label="Company Name"
          name="company"
          type="text"
          autoComplete="organization"
          required
        />
        <TextField
          className="col-span-full"
          label="Website URL"
          name="website"
          type="url"
          placeholder="https://yourwebsite.com"
          required
        />
        <SelectField
          className="col-span-full"
          label="Industry"
          name="industry"
        >
          <option value="">Select your industry</option>
          <option value="igaming-casino">iGaming - Online Casino</option>
          <option value="igaming-sportsbook">iGaming - Sports Betting</option>
          <option value="igaming-poker">iGaming - Poker</option>
          <option value="igaming-affiliate">iGaming - Affiliate Network</option>
          <option value="ecommerce">E-commerce</option>
          <option value="saas">SaaS</option>
          <option value="fintech">Financial Services</option>
          <option value="travel">Travel & Hospitality</option>
          <option value="media">Media & Publishing</option>
          <option value="other">Other</option>
        </SelectField>
        <SelectField
          className="col-span-full"
          label="How did you hear about us?"
          name="referral_source"
        >
          <option>AltaVista search</option>
          <option>Super Bowl commercial</option>
          <option>Our route 34 city bus ad</option>
          <option>The “Never Use This” podcast</option>
        </SelectField>
        <div className="col-span-full">
          <Button type="submit" variant="solid" color="rankrender" className="w-full">
            <span>
              Get Started Free <span aria-hidden="true">&rarr;</span>
            </span>
          </Button>
        </div>
      </form>

      <div className="mt-8 text-center">
        <div className="text-sm text-gray-600 space-y-2">
          <div className="flex items-center justify-center gap-x-4">
            <div className="flex items-center">
              <svg className="mr-2 h-4 w-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clipRule="evenodd" />
              </svg>
              Free forever plan
            </div>
            <div className="flex items-center">
              <svg className="mr-2 h-4 w-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clipRule="evenodd" />
              </svg>
              No credit card required
            </div>
          </div>
          <div className="flex items-center justify-center">
            <div className="flex items-center">
              <svg className="mr-2 h-4 w-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clipRule="evenodd" />
              </svg>
              Upgrade anytime
            </div>
          </div>
        </div>
      </div>
    </SlimLayout>
  )
}
