import { type Metadata } from 'next'
import { Container } from '@/components/Container'
import { Header } from '@/components/Header'
import { Footer } from '@/components/Footer'

export const metadata: Metadata = {
  title: 'Features',
  description: 'Discover how RankRender optimizes iGaming platforms for search engines, social media, and AI tools.',
}

const features = [
  {
    name: 'Lightning-Fast Rendering',
    description: 'Deliver your JavaScript content to search bots in milliseconds, not seconds. Our optimized rendering engine ensures crawlers can access your content instantly.',
    icon: (
      <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" d="M3.75 13.5l10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75z" />
      </svg>
    ),
  },
  {
    name: 'JavaScript SEO Optimization',
    description: 'Advanced optimization for JavaScript-heavy platforms including SPAs, dynamic content, and interactive applications. Perfect for modern web applications across all industries.',
    icon: (
      <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" d="M17.25 6.75L22.5 12l-5.25 5.25m-10.5 0L1.5 12l5.25-5.25m7.5-3l-4.5 16.5" />
      </svg>
    ),
  },
  {
    name: 'AI Search Visibility',
    description: 'Get discovered by ChatGPT, Claude, Perplexity, and other AI search engines. Future-proof your SEO strategy for the age of AI-powered search.',
    icon: (
      <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 001.423 1.423l1.183.394-1.183.394a2.25 2.25 0 00-1.423 1.423z" />
      </svg>
    ),
  },
  {
    name: 'Social Media Previews',
    description: 'Perfect link previews on Facebook, Twitter, LinkedIn, and WhatsApp. Showcase your products, content, and features with rich, engaging previews.',
    icon: (
      <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" d="M7.217 10.907a2.25 2.25 0 100 2.186m0-2.186c.18.324.283.696.283 1.093s-.103.77-.283 1.093m0-2.186l9.566-5.314m-9.566 7.5l9.566 5.314m0 0a2.25 2.25 0 103.935 2.186 2.25 2.25 0 00-3.935-2.186zm0-12.814a2.25 2.25 0 103.933-2.185 2.25 2.25 0 00-3.933 2.185z" />
      </svg>
    ),
  },
  {
    name: 'E-commerce Optimization',
    description: 'Specialized features for product catalogs, inventory updates, and dynamic pricing. Perfect for online stores and marketplaces.',
    icon: (
      <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 10.5V6a3.75 3.75 0 10-7.5 0v4.5m11.356-1.993l1.263 12c.07.665-.45 1.243-1.119 1.243H4.25a1.125 1.125 0 01-1.12-1.243l1.264-12A1.125 1.125 0 015.513 7.5h12.974c.576 0 1.059.435 1.119.993zM8.625 10.5a.375.375 0 11-.75 0 .375.375 0 01.75 0zm7.5 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z" />
      </svg>
    ),
  },
  {
    name: 'Real-Time Analytics',
    description: 'Monitor your SEO performance with detailed analytics. Track crawl rates, indexing status, and search visibility across all major search engines.',
    icon: (
      <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" d="M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 013 19.875v-6.75zM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V8.625zM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V4.125z" />
      </svg>
    ),
  },
  {
    name: 'Content Management',
    description: 'Optimize blogs, documentation, user-generated content, and multimedia. Perfect for content-heavy platforms and media sites.',
    icon: (
      <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z" />
      </svg>
    ),
  },
]

const benefits = [
  {
    title: 'Faster Indexing',
    description: 'Get your new products, content, and features indexed by search engines up to 10x faster.',
    stat: '10x',
    statLabel: 'Faster indexing',
  },
  {
    title: 'Better Crawl Budget',
    description: 'Optimize how search engines crawl your site, ensuring important pages get priority.',
    stat: '300%',
    statLabel: 'More efficient crawling',
  },
  {
    title: 'Increased Visibility',
    description: 'Improve your search rankings and get discovered by more potential players.',
    stat: '150%',
    statLabel: 'Increase in organic traffic',
  },
  {
    title: 'Enhanced Performance',
    description: 'Boost your PageSpeed scores and provide better user experience.',
    stat: '40%',
    statLabel: 'Improvement in Core Web Vitals',
  },
]

export default function Features() {
  return (
    <>
      <Header />
      <main>
        {/* Hero Section */}
        <div className="bg-white py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl text-center">
              <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl">
                Features built for modern web
              </h1>
              <p className="mt-6 text-lg leading-8 text-gray-600">
                RankRender provides advanced SEO optimization for JavaScript-heavy platforms, dynamic content, and modern web applications.
                Get discovered faster by search engines, social media, and AI tools across all industries.
              </p>
            </div>
          </Container>
        </div>

        {/* Features Grid */}
        <div className="bg-gray-50 py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl lg:text-center">
              <h2 className="text-base font-semibold leading-7 text-rankrender-600">Everything you need</h2>
              <p className="mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                Powerful features for iGaming SEO
              </p>
              <p className="mt-6 text-lg leading-8 text-gray-600">
                Our platform is specifically designed to handle the unique challenges of iGaming SEO,
                from dynamic content to compliance requirements.
              </p>
            </div>
            <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
              <dl className="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-3">
                {features.map((feature) => (
                  <div key={feature.name} className="flex flex-col">
                    <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900">
                      <div className="h-10 w-10 flex items-center justify-center rounded-lg bg-rankrender-600 text-white">
                        {feature.icon}
                      </div>
                      {feature.name}
                    </dt>
                    <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                      <p className="flex-auto">{feature.description}</p>
                    </dd>
                  </div>
                ))}
              </dl>
            </div>
          </Container>
        </div>

        {/* Benefits Section */}
        <div className="bg-white py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl lg:text-center">
              <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                Proven results for iGaming platforms
              </h2>
              <p className="mt-6 text-lg leading-8 text-gray-600">
                See the impact RankRender can have on your casino, sportsbook, or poker platform's SEO performance.
              </p>
            </div>
            <div className="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-x-8 gap-y-16 sm:mt-20 lg:mx-0 lg:max-w-none lg:grid-cols-2 lg:gap-y-20">
              {benefits.map((benefit) => (
                <div key={benefit.title} className="flex flex-col lg:flex-row lg:gap-x-8">
                  <div className="lg:min-w-0 lg:flex-1">
                    <h3 className="text-lg font-semibold leading-8 tracking-tight text-gray-900">
                      {benefit.title}
                    </h3>
                    <p className="mt-2 text-base leading-7 text-gray-600">
                      {benefit.description}
                    </p>
                  </div>
                  <div className="mt-6 flex flex-col items-start lg:mt-0 lg:flex-shrink-0">
                    <div className="text-3xl font-bold tracking-tight text-rankrender-600">
                      {benefit.stat}
                    </div>
                    <div className="text-sm leading-6 text-gray-600">
                      {benefit.statLabel}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </Container>
        </div>

        {/* How it works */}
        <div className="bg-gray-50 py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl lg:text-center">
              <h2 className="text-base font-semibold leading-7 text-rankrender-600">How it works</h2>
              <p className="mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                Simple setup, powerful results
              </p>
            </div>
            <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
              <div className="grid max-w-xl grid-cols-1 gap-y-8 lg:max-w-none lg:grid-cols-4 lg:gap-x-8">
                <div className="flex flex-col items-center text-center">
                  <div className="flex h-16 w-16 items-center justify-center rounded-full bg-rankrender-600 text-white text-xl font-bold">
                    1
                  </div>
                  <h3 className="mt-6 text-lg font-semibold leading-8 text-gray-900">
                    Quick Integration
                  </h3>
                  <p className="mt-2 text-base leading-7 text-gray-600">
                    Add our lightweight script to your iGaming platform. Works with all major frameworks and platforms.
                  </p>
                </div>
                <div className="flex flex-col items-center text-center">
                  <div className="flex h-16 w-16 items-center justify-center rounded-full bg-rankrender-600 text-white text-xl font-bold">
                    2
                  </div>
                  <h3 className="mt-6 text-lg font-semibold leading-8 text-gray-900">
                    Automatic Optimization
                  </h3>
                  <p className="mt-2 text-base leading-7 text-gray-600">
                    Our system automatically detects and optimizes your content for search engines and social media.
                  </p>
                </div>
                <div className="flex flex-col items-center text-center">
                  <div className="flex h-16 w-16 items-center justify-center rounded-full bg-rankrender-600 text-white text-xl font-bold">
                    3
                  </div>
                  <h3 className="mt-6 text-lg font-semibold leading-8 text-gray-900">
                    Smart Caching
                  </h3>
                  <p className="mt-2 text-base leading-7 text-gray-600">
                    Intelligent caching ensures fast delivery to bots while keeping your live content fresh for users.
                  </p>
                </div>
                <div className="flex flex-col items-center text-center">
                  <div className="flex h-16 w-16 items-center justify-center rounded-full bg-rankrender-600 text-white text-xl font-bold">
                    4
                  </div>
                  <h3 className="mt-6 text-lg font-semibold leading-8 text-gray-900">
                    Monitor & Improve
                  </h3>
                  <p className="mt-2 text-base leading-7 text-gray-600">
                    Track your SEO performance with detailed analytics and continuous optimization recommendations.
                  </p>
                </div>
              </div>
            </div>
          </Container>
        </div>
      </main>
      <Footer />
    </>
  )
}
