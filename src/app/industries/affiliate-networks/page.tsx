import { type Metadata } from 'next'
import { Container } from '@/components/Container'
import { Header } from '@/components/Header'
import { Footer } from '@/components/Footer'
import { Button } from '@/components/Button'

export const metadata: Metadata = {
  title: 'Affiliate Network SEO Solutions',
  description: 'Specialized SEO optimization for affiliate networks. Maximize affiliate link performance and tracking.',
}

export default function AffiliateNetworks() {
  return (
    <>
      <Header />
      <main>
        {/* Hero Section */}
        <div className="bg-white py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl text-center">
              <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl">
                Affiliate Network SEO Solutions
              </h1>
              <p className="mt-6 text-lg leading-8 text-gray-600">
                Specialized SEO optimization for affiliate networks and marketing platforms. 
                Maximize affiliate link performance, tracking, and partner discovery.
              </p>
              <div className="mt-10 flex items-center justify-center gap-x-6">
                <Button href="/register">Start Free Trial</Button>
                <Button href="/contact" variant="outline">
                  Get Affiliate Demo
                </Button>
              </div>
            </div>
          </Container>
        </div>

        {/* Affiliate SEO Challenges */}
        <div className="bg-gray-50 py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl lg:text-center">
              <h2 className="text-base font-semibold leading-7 text-rankrender-600">Affiliate SEO challenges</h2>
              <p className="mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                Unique challenges for affiliate networks
              </p>
              <p className="mt-6 text-lg leading-8 text-gray-600">
                Affiliate networks face specific SEO challenges related to link tracking, partner content, and performance optimization.
              </p>
            </div>
            <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
              <dl className="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-2">
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-red-600">
                    <span className="text-2xl">🔗</span>
                    Link Tracking Complexity
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      Affiliate tracking links and redirects can confuse search engines, making it difficult to properly 
                      index affiliate content and track performance across different partners.
                    </p>
                  </dd>
                </div>
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-red-600">
                    <span className="text-2xl">👥</span>
                    Partner Content Visibility
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      Affiliate partner profiles, commission structures, and promotional materials are often hidden behind 
                      login walls or generated dynamically, missing valuable search traffic.
                    </p>
                  </dd>
                </div>
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-red-600">
                    <span className="text-2xl">📊</span>
                    Performance Data Isolation
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      Real-time performance data, commission rates, and conversion statistics are typically in dashboards 
                      that search engines can't access, limiting discovery opportunities.
                    </p>
                  </dd>
                </div>
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-red-600">
                    <span className="text-2xl">🎯</span>
                    Multi-Vertical Complexity
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      Affiliate networks often span multiple industries and verticals, creating complex content structures 
                      that are difficult to organize and optimize for search engines.
                    </p>
                  </dd>
                </div>
              </dl>
            </div>
          </Container>
        </div>

        {/* RankRender Solutions */}
        <div className="bg-white py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl lg:text-center">
              <h2 className="text-base font-semibold leading-7 text-rankrender-600">RankRender solutions</h2>
              <p className="mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                How we optimize affiliate networks
              </p>
              <p className="mt-6 text-lg leading-8 text-gray-600">
                Our specialized affiliate optimization ensures every partner, offer, and performance metric is discoverable.
              </p>
            </div>
            <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
              <dl className="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-2">
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-green-600">
                    <svg className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Smart Link Optimization
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      Affiliate tracking links are optimized to maintain SEO value while preserving tracking functionality, 
                      ensuring both search engines and affiliate systems can properly process links.
                    </p>
                  </dd>
                </div>
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-green-600">
                    <svg className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Partner Profile Indexing
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      Affiliate partner profiles, commission structures, and promotional materials are made searchable, 
                      helping potential partners discover opportunities and improving network visibility.
                    </p>
                  </dd>
                </div>
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-green-600">
                    <svg className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Performance Data Visibility
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      Aggregated performance data and success stories are optimized for search discovery, 
                      while maintaining privacy and competitive sensitivity of individual metrics.
                    </p>
                  </dd>
                </div>
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-green-600">
                    <svg className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Multi-Vertical Organization
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      Complex multi-vertical content is organized with proper categorization and structured data, 
                      making it easy for search engines to understand and rank different industry offerings.
                    </p>
                  </dd>
                </div>
              </dl>
            </div>
          </Container>
        </div>

        {/* Affiliate Verticals */}
        <div className="bg-gray-50 py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl lg:text-center">
              <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                Optimize across all verticals
              </h2>
              <p className="mt-6 text-lg leading-8 text-gray-600">
                Comprehensive optimization for affiliate networks spanning multiple industries and niches.
              </p>
            </div>
            <div className="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-x-8 gap-y-16 sm:mt-20 lg:mx-0 lg:max-w-none lg:grid-cols-3">
              <div className="flex flex-col items-start">
                <div className="flex items-center gap-x-3">
                  <span className="text-3xl">🎰</span>
                  <h3 className="text-lg font-semibold leading-8 tracking-tight text-gray-900">
                    iGaming Affiliates
                  </h3>
                </div>
                <p className="mt-4 text-base leading-7 text-gray-600">
                  Optimize casino, sportsbook, and poker affiliate programs with compliance-aware SEO strategies.
                </p>
                <ul className="mt-4 space-y-2 text-sm text-gray-600">
                  <li>• Casino affiliate programs</li>
                  <li>• Sports betting partnerships</li>
                  <li>• Poker room affiliates</li>
                  <li>• Compliance tracking</li>
                </ul>
              </div>
              <div className="flex flex-col items-start">
                <div className="flex items-center gap-x-3">
                  <span className="text-3xl">🛒</span>
                  <h3 className="text-lg font-semibold leading-8 tracking-tight text-gray-900">
                    E-commerce Affiliates
                  </h3>
                </div>
                <p className="mt-4 text-base leading-7 text-gray-600">
                  Enhance discoverability of product affiliate programs, commission structures, and promotional materials.
                </p>
                <ul className="mt-4 space-y-2 text-sm text-gray-600">
                  <li>• Product affiliate programs</li>
                  <li>• Commission rate optimization</li>
                  <li>• Promotional content</li>
                  <li>• Seasonal campaigns</li>
                </ul>
              </div>
              <div className="flex flex-col items-start">
                <div className="flex items-center gap-x-3">
                  <span className="text-3xl">💼</span>
                  <h3 className="text-lg font-semibold leading-8 tracking-tight text-gray-900">
                    SaaS & Tech Affiliates
                  </h3>
                </div>
                <p className="mt-4 text-base leading-7 text-gray-600">
                  Optimize software affiliate programs, partner resources, and technical integration guides.
                </p>
                <ul className="mt-4 space-y-2 text-sm text-gray-600">
                  <li>• Software affiliate programs</li>
                  <li>• Integration documentation</li>
                  <li>• Partner resource centers</li>
                  <li>• API affiliate tracking</li>
                </ul>
              </div>
            </div>
          </Container>
        </div>

        {/* Results & Benefits */}
        <div className="bg-white py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl lg:text-center">
              <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                Proven results for affiliate networks
              </h2>
              <p className="mt-6 text-lg leading-8 text-gray-600">
                Affiliate networks using RankRender see significant improvements in partner acquisition and program visibility.
              </p>
            </div>
            <div className="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-x-8 gap-y-16 sm:mt-20 lg:mx-0 lg:max-w-none lg:grid-cols-4">
              <div className="flex flex-col items-center text-center">
                <div className="text-4xl font-bold tracking-tight text-rankrender-600">
                  280%
                </div>
                <div className="text-sm leading-6 text-gray-600 mt-2">
                  Increase in partner discovery
                </div>
                <p className="mt-4 text-sm text-gray-500">
                  More affiliates find and join programs organically
                </p>
              </div>
              <div className="flex flex-col items-center text-center">
                <div className="text-4xl font-bold tracking-tight text-rankrender-600">
                  75%
                </div>
                <div className="text-sm leading-6 text-gray-600 mt-2">
                  Faster program indexing
                </div>
                <p className="mt-4 text-sm text-gray-500">
                  New affiliate programs appear in search quickly
                </p>
              </div>
              <div className="flex flex-col items-center text-center">
                <div className="text-4xl font-bold tracking-tight text-rankrender-600">
                  190%
                </div>
                <div className="text-sm leading-6 text-gray-600 mt-2">
                  More organic traffic
                </div>
                <p className="mt-4 text-sm text-gray-500">
                  Increased organic traffic to affiliate programs
                </p>
              </div>
              <div className="flex flex-col items-center text-center">
                <div className="text-4xl font-bold tracking-tight text-rankrender-600">
                  100%
                </div>
                <div className="text-sm leading-6 text-gray-600 mt-2">
                  Link tracking maintained
                </div>
                <p className="mt-4 text-sm text-gray-500">
                  SEO optimization without breaking tracking
                </p>
              </div>
            </div>
          </Container>
        </div>

        {/* CTA Section */}
        <div className="bg-rankrender-600 py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl text-center">
              <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl">
                Ready to optimize your affiliate network?
              </h2>
              <p className="mx-auto mt-6 max-w-xl text-lg leading-8 text-rankrender-100">
                Join leading affiliate networks that trust RankRender to optimize their programs and attract more partners.
              </p>
              <div className="mt-10 flex items-center justify-center gap-x-6">
                <Button href="/register" color="white">
                  Start Free Trial
                </Button>
                <Button href="/contact" variant="outline" color="white">
                  Get Affiliate Demo
                </Button>
              </div>
            </div>
          </Container>
        </div>
      </main>
      <Footer />
    </>
  )
}
