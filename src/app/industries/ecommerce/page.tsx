import { type Metadata } from 'next'
import { Container } from '@/components/Container'
import { Header } from '@/components/Header'
import { Footer } from '@/components/Footer'
import { Button } from '@/components/Button'

export const metadata: Metadata = {
  title: 'E-commerce Industry SEO Solutions',
  description: 'Specialized SEO optimization for e-commerce platforms. Optimize product catalogs and shopping experiences.',
}

export default function EcommerceIndustry() {
  return (
    <>
      <Header />
      <main>
        {/* Hero Section */}
        <div className="bg-white py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl text-center">
              <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl">
                E-commerce Industry SEO
              </h1>
              <p className="mt-6 text-lg leading-8 text-gray-600">
                Specialized SEO optimization for e-commerce platforms and online retailers. 
                Optimize product catalogs, shopping experiences, and customer journeys for maximum search visibility.
              </p>
              <div className="mt-10 flex items-center justify-center gap-x-6">
                <Button href="/register">Start Free Trial</Button>
                <Button href="/contact" variant="outline">
                  Get E-commerce Demo
                </Button>
              </div>
            </div>
          </Container>
        </div>

        {/* E-commerce Landscape */}
        <div className="bg-gray-50 py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl lg:text-center">
              <h2 className="text-base font-semibold leading-7 text-rankrender-600">E-commerce landscape</h2>
              <p className="mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                The competitive world of online retail
              </p>
              <p className="mt-6 text-lg leading-8 text-gray-600">
                E-commerce businesses face intense competition for search visibility, making technical SEO optimization crucial for success.
              </p>
            </div>
            <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
              <dl className="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-2">
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900">
                    <span className="text-2xl">🛒</span>
                    Retail Competition
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      Online retailers compete against Amazon, major brands, and thousands of other e-commerce sites 
                      for product search visibility and customer attention.
                    </p>
                  </dd>
                </div>
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900">
                    <span className="text-2xl">📱</span>
                    Mobile Shopping Growth
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      Mobile commerce continues to grow rapidly, requiring e-commerce sites to optimize for mobile search 
                      and provide seamless mobile shopping experiences.
                    </p>
                  </dd>
                </div>
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900">
                    <span className="text-2xl">🔍</span>
                    Product Discovery
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      Customers increasingly use search engines to discover products, compare prices, and read reviews 
                      before making purchasing decisions, making SEO critical for sales.
                    </p>
                  </dd>
                </div>
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900">
                    <span className="text-2xl">⚡</span>
                    Speed & Performance
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      Page speed and Core Web Vitals directly impact both search rankings and conversion rates, 
                      making technical optimization essential for e-commerce success.
                    </p>
                  </dd>
                </div>
              </dl>
            </div>
          </Container>
        </div>

        {/* E-commerce SEO Strategies */}
        <div className="bg-white py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl lg:text-center">
              <h2 className="text-base font-semibold leading-7 text-rankrender-600">E-commerce SEO strategies</h2>
              <p className="mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                Comprehensive optimization approach
              </p>
              <p className="mt-6 text-lg leading-8 text-gray-600">
                Our e-commerce SEO strategies cover every aspect of your online store to maximize search visibility and sales.
              </p>
            </div>
            <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
              <dl className="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-2">
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-green-600">
                    <svg className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Product Page Optimization
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      Optimize individual product pages with rich snippets, detailed descriptions, customer reviews, 
                      and structured data to improve search visibility and click-through rates.
                    </p>
                  </dd>
                </div>
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-green-600">
                    <svg className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Category & Navigation SEO
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      Enhance category pages, navigation structures, and faceted search to create SEO-friendly URLs 
                      and ensure all product variations are discoverable by search engines.
                    </p>
                  </dd>
                </div>
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-green-600">
                    <svg className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Technical Performance
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      Optimize Core Web Vitals, page speed, and mobile performance to improve both search rankings 
                      and conversion rates across all devices and shopping scenarios.
                    </p>
                  </dd>
                </div>
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-green-600">
                    <svg className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Content Marketing Integration
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      Create and optimize buying guides, product comparisons, and educational content 
                      to capture customers throughout their purchase journey and build topical authority.
                    </p>
                  </dd>
                </div>
              </dl>
            </div>
          </Container>
        </div>

        {/* E-commerce Verticals */}
        <div className="bg-gray-50 py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl lg:text-center">
              <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                Optimize across e-commerce verticals
              </h2>
              <p className="mt-6 text-lg leading-8 text-gray-600">
                Specialized optimization strategies for different types of e-commerce businesses and product categories.
              </p>
            </div>
            <div className="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-x-8 gap-y-16 sm:mt-20 lg:mx-0 lg:max-w-none lg:grid-cols-3">
              <div className="flex flex-col items-start">
                <div className="flex items-center gap-x-3">
                  <span className="text-3xl">👕</span>
                  <h3 className="text-lg font-semibold leading-8 tracking-tight text-gray-900">
                    Fashion & Apparel
                  </h3>
                </div>
                <p className="mt-4 text-base leading-7 text-gray-600">
                  Optimize clothing, accessories, and fashion products with size guides, color variations, and seasonal collections.
                </p>
                <ul className="mt-4 space-y-2 text-sm text-gray-600">
                  <li>• Size and fit optimization</li>
                  <li>• Color and style variants</li>
                  <li>• Seasonal collection SEO</li>
                  <li>• Fashion trend targeting</li>
                </ul>
              </div>
              <div className="flex flex-col items-start">
                <div className="flex items-center gap-x-3">
                  <span className="text-3xl">📱</span>
                  <h3 className="text-lg font-semibold leading-8 tracking-tight text-gray-900">
                    Electronics & Tech
                  </h3>
                </div>
                <p className="mt-4 text-base leading-7 text-gray-600">
                  Enhance discoverability of electronics with detailed specifications, compatibility information, and technical reviews.
                </p>
                <ul className="mt-4 space-y-2 text-sm text-gray-600">
                  <li>• Technical specification SEO</li>
                  <li>• Compatibility optimization</li>
                  <li>• Review and rating features</li>
                  <li>• Product comparison tools</li>
                </ul>
              </div>
              <div className="flex flex-col items-start">
                <div className="flex items-center gap-x-3">
                  <span className="text-3xl">🏠</span>
                  <h3 className="text-lg font-semibold leading-8 tracking-tight text-gray-900">
                    Home & Garden
                  </h3>
                </div>
                <p className="mt-4 text-base leading-7 text-gray-600">
                  Optimize home goods, furniture, and garden products with room-specific targeting and lifestyle content.
                </p>
                <ul className="mt-4 space-y-2 text-sm text-gray-600">
                  <li>• Room-based categorization</li>
                  <li>• Lifestyle content integration</li>
                  <li>• Seasonal product optimization</li>
                  <li>• DIY and how-to guides</li>
                </ul>
              </div>
            </div>
          </Container>
        </div>

        {/* Success Metrics */}
        <div className="bg-white py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl lg:text-center">
              <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                E-commerce SEO success metrics
              </h2>
              <p className="mt-6 text-lg leading-8 text-gray-600">
                Track the metrics that matter most for e-commerce SEO success and business growth.
              </p>
            </div>
            <div className="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-x-8 gap-y-16 sm:mt-20 lg:mx-0 lg:max-w-none lg:grid-cols-4">
              <div className="flex flex-col items-center text-center">
                <div className="text-4xl font-bold tracking-tight text-rankrender-600">
                  340%
                </div>
                <div className="text-sm leading-6 text-gray-600 mt-2">
                  Average organic traffic increase
                </div>
                <p className="mt-4 text-sm text-gray-500">
                  More customers discovering products organically
                </p>
              </div>
              <div className="flex flex-col items-center text-center">
                <div className="text-4xl font-bold tracking-tight text-rankrender-600">
                  220%
                </div>
                <div className="text-sm leading-6 text-gray-600 mt-2">
                  Increase in organic revenue
                </div>
                <p className="mt-4 text-sm text-gray-500">
                  Higher sales from search engine traffic
                </p>
              </div>
              <div className="flex flex-col items-center text-center">
                <div className="text-4xl font-bold tracking-tight text-rankrender-600">
                  85%
                </div>
                <div className="text-sm leading-6 text-gray-600 mt-2">
                  Improvement in page speed
                </div>
                <p className="mt-4 text-sm text-gray-500">
                  Faster loading times boost conversions
                </p>
              </div>
              <div className="flex flex-col items-center text-center">
                <div className="text-4xl font-bold tracking-tight text-rankrender-600">
                  95%
                </div>
                <div className="text-sm leading-6 text-gray-600 mt-2">
                  Product catalog coverage
                </div>
                <p className="mt-4 text-sm text-gray-500">
                  All products properly indexed and optimized
                </p>
              </div>
            </div>
          </Container>
        </div>

        {/* Case Study Preview */}
        <div className="bg-gray-50 py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl lg:text-center">
              <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                E-commerce success story
              </h2>
              <p className="mt-6 text-lg leading-8 text-gray-600">
                See how a leading fashion retailer transformed their SEO performance with RankRender.
              </p>
            </div>
            <div className="mx-auto mt-16 max-w-4xl">
              <div className="rounded-2xl bg-white p-8 shadow-lg">
                <div className="flex items-center gap-x-4 mb-6">
                  <div className="h-12 w-12 rounded-full bg-rankrender-100 flex items-center justify-center">
                    <span className="text-xl">👕</span>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">Fashion Forward Retailer</h3>
                    <p className="text-sm text-gray-600">Online fashion and accessories store</p>
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-rankrender-600">450%</div>
                    <div className="text-sm text-gray-600">Organic traffic increase</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-rankrender-600">320%</div>
                    <div className="text-sm text-gray-600">Revenue growth</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-rankrender-600">6 months</div>
                    <div className="text-sm text-gray-600">Time to results</div>
                  </div>
                </div>
                
                <blockquote className="text-gray-700 italic">
                  "RankRender transformed our product discovery. Our seasonal collections now rank on the first page 
                  for competitive fashion keywords, and our organic revenue has more than tripled."
                </blockquote>
                
                <div className="mt-6">
                  <Button href="/resources/case-studies" variant="outline">
                    Read Full Case Study
                  </Button>
                </div>
              </div>
            </div>
          </Container>
        </div>

        {/* CTA Section */}
        <div className="bg-rankrender-600 py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl text-center">
              <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl">
                Ready to dominate e-commerce search?
              </h2>
              <p className="mx-auto mt-6 max-w-xl text-lg leading-8 text-rankrender-100">
                Join successful e-commerce brands that trust RankRender to optimize their stores and drive more sales.
              </p>
              <div className="mt-10 flex items-center justify-center gap-x-6">
                <Button href="/register" color="white">
                  Start Free Trial
                </Button>
                <Button href="/contact" variant="outline" color="white">
                  Get E-commerce Demo
                </Button>
              </div>
            </div>
          </Container>
        </div>
      </main>
      <Footer />
    </>
  )
}
