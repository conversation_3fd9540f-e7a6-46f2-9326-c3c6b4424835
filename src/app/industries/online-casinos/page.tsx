import { type Metadata } from 'next'
import { Container } from '@/components/Container'
import { Header } from '@/components/Header'
import { Footer } from '@/components/Footer'
import { Button } from '@/components/Button'

export const metadata: Metadata = {
  title: 'Online Casinos',
  description: 'SEO solutions specifically designed for online casinos. Optimize your slot games, table games, and live dealer content.',
}

export default function OnlineCasinos() {
  return (
    <>
      <Header />
      <main>
        {/* Hero Section */}
        <div className="bg-white py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl text-center">
              <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl">
                SEO for Online Casinos
              </h1>
              <p className="mt-6 text-lg leading-8 text-gray-600">
                Specialized optimization for casino platforms. Get your slot games, table games, and live dealer content 
                discovered by more players while maintaining compliance.
              </p>
              <div className="mt-10 flex items-center justify-center gap-x-6">
                <Button href="/register">Start Free Trial</Button>
                <Button href="/contact" variant="outline">
                  Get Custom Quote
                </Button>
              </div>
            </div>
          </Container>
        </div>

        {/* Casino-Specific Challenges */}
        <div className="bg-gray-50 py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl lg:text-center">
              <h2 className="text-base font-semibold leading-7 text-rankrender-600">Casino challenges</h2>
              <p className="mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                Unique SEO challenges for online casinos
              </p>
              <p className="mt-6 text-lg leading-8 text-gray-600">
                Online casinos face specific SEO hurdles that require specialized solutions.
              </p>
            </div>
            <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
              <dl className="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-2">
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900">
                    <span className="text-2xl">🎰</span>
                    Game Library Indexing
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      With thousands of slot games and table games, ensuring your entire game library is properly indexed and discoverable is a massive challenge. New games need to be crawled quickly to capture player interest.
                    </p>
                  </dd>
                </div>
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900">
                    <span className="text-2xl">🎁</span>
                    Promotion Visibility
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      Casino promotions and bonuses change frequently. Search engines struggle to keep up with your latest offers, meaning potential players miss out on your best deals.
                    </p>
                  </dd>
                </div>
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900">
                    <span className="text-2xl">🎮</span>
                    Live Dealer Content
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      Live dealer games and real-time content are difficult for search engines to understand and index. This dynamic content often goes unnoticed by search crawlers.
                    </p>
                  </dd>
                </div>
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900">
                    <span className="text-2xl">🏆</span>
                    Tournament Pages
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      Casino tournaments and leaderboards are time-sensitive content that needs immediate indexing. Traditional SEO approaches are too slow for these dynamic pages.
                    </p>
                  </dd>
                </div>
              </dl>
            </div>
          </Container>
        </div>

        {/* Solutions for Casinos */}
        <div className="bg-white py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl lg:text-center">
              <h2 className="text-base font-semibold leading-7 text-rankrender-600">Our casino solutions</h2>
              <p className="mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                Optimized specifically for casino platforms
              </p>
              <p className="mt-6 text-lg leading-8 text-gray-600">
                RankRender provides specialized features designed to handle the unique content and SEO needs of online casinos.
              </p>
            </div>
            <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
              <dl className="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-3">
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900">
                    <div className="h-10 w-10 flex items-center justify-center rounded-lg bg-rankrender-600 text-white">
                      <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" d="M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z" />
                      </svg>
                    </div>
                    Game Discovery Optimization
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      Ensure every slot game, table game, and live dealer option is properly indexed. Our system automatically optimizes game pages for search engines and creates rich snippets for better visibility.
                    </p>
                  </dd>
                </div>
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900">
                    <div className="h-10 w-10 flex items-center justify-center rounded-lg bg-rankrender-600 text-white">
                      <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" d="M16.5 18.75h-9m9 0a3 3 0 013 3h-15a3 3 0 013-3m9 0v-3.375c0-.621-.503-1.125-1.125-1.125h-.871M7.5 18.75v-3.375c0-.621.504-1.125 1.125-1.125h.872m5.007 0H9.497m5.007 0a7.454 7.454 0 01-.982-3.172M9.497 14.25a7.454 7.454 0 00.981-3.172M5.25 4.236c-.982.143-1.954.317-2.916.52A6.003 6.003 0 007.73 9.728M5.25 4.236V4.5c0 2.108.966 3.99 2.48 5.228M5.25 4.236V2.721C7.456 2.41 9.71 2.25 12 2.25c2.291 0 4.545.16 6.75.47v1.516M7.73 9.728a6.726 6.726 0 002.748 1.35m8.272-6.842V4.5c0 2.108-.966 3.99-2.48 5.228m2.48-5.228a25.628 25.628 0 012.916.52 6.003 6.003 0 00-5.395 4.972m0 0a6.726 6.726 0 01-2.749 1.35m0 0A7.454 7.454 0 0112 14.25M15.75 9.75a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0z" />
                      </svg>
                    </div>
                    Promotion & Bonus SEO
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      Keep your latest bonuses and promotions visible in search results. Real-time updates ensure players always see your current offers, not expired ones.
                    </p>
                  </dd>
                </div>
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900">
                    <div className="h-10 w-10 flex items-center justify-center rounded-lg bg-rankrender-600 text-white">
                      <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 10.5l4.72-4.72a.75.75 0 011.28.53v11.38a.75.75 0 01-1.28.53l-4.72-4.72M4.5 18.75h9a2.25 2.25 0 002.25-2.25v-9a2.25 2.25 0 00-2.25-2.25h-9A2.25 2.25 0 002.25 7.5v9a2.25 2.25 0 002.25 2.25z" />
                      </svg>
                    </div>
                    Live Content Optimization
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      Optimize live dealer games, tournaments, and real-time content for search engines. Our system captures and indexes dynamic content that traditional crawlers miss.
                    </p>
                  </dd>
                </div>
              </dl>
            </div>
          </Container>
        </div>

        {/* Success Metrics */}
        <div className="bg-gray-50 py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl lg:text-center">
              <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                Proven results for casino operators
              </h2>
              <p className="mt-6 text-lg leading-8 text-gray-600">
                See how RankRender has helped casino platforms improve their SEO performance and player acquisition.
              </p>
            </div>
            <div className="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-x-8 gap-y-16 sm:mt-20 lg:mx-0 lg:max-w-none lg:grid-cols-4">
              <div className="flex flex-col items-center text-center">
                <div className="text-4xl font-bold tracking-tight text-rankrender-600">
                  85%
                </div>
                <div className="text-sm leading-6 text-gray-600 mt-2">
                  Faster game indexing
                </div>
                <p className="mt-4 text-sm text-gray-500">
                  New slot games and table games get indexed 85% faster, capturing player interest immediately.
                </p>
              </div>
              <div className="flex flex-col items-center text-center">
                <div className="text-4xl font-bold tracking-tight text-rankrender-600">
                  200%
                </div>
                <div className="text-sm leading-6 text-gray-600 mt-2">
                  Increase in promotion visibility
                </div>
                <p className="mt-4 text-sm text-gray-500">
                  Casino bonuses and promotions get 200% more visibility in search results.
                </p>
              </div>
              <div className="flex flex-col items-center text-center">
                <div className="text-4xl font-bold tracking-tight text-rankrender-600">
                  60%
                </div>
                <div className="text-sm leading-6 text-gray-600 mt-2">
                  Improvement in PageSpeed
                </div>
                <p className="mt-4 text-sm text-gray-500">
                  Casino platforms see average 60% improvement in PageSpeed scores without losing functionality.
                </p>
              </div>
              <div className="flex flex-col items-center text-center">
                <div className="text-4xl font-bold tracking-tight text-rankrender-600">
                  150%
                </div>
                <div className="text-sm leading-6 text-gray-600 mt-2">
                  More organic traffic
                </div>
                <p className="mt-4 text-sm text-gray-500">
                  Casino operators typically see 150% increase in organic search traffic within 3 months.
                </p>
              </div>
            </div>
          </Container>
        </div>

        {/* Features for Casinos */}
        <div className="bg-white py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl lg:text-center">
              <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                Features designed for casino success
              </h2>
            </div>
            <div className="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-x-8 gap-y-16 sm:mt-20 lg:mx-0 lg:max-w-none lg:grid-cols-2 lg:gap-y-20">
              <div className="flex flex-col">
                <h3 className="text-lg font-semibold leading-8 tracking-tight text-gray-900">
                  🎯 Smart Game Categorization
                </h3>
                <p className="mt-2 text-base leading-7 text-gray-600">
                  Automatically categorize and optimize slot games by provider, theme, features, and RTP. Create SEO-friendly game collections that players can easily discover.
                </p>
              </div>
              <div className="flex flex-col">
                <h3 className="text-lg font-semibold leading-8 tracking-tight text-gray-900">
                  💰 Bonus Tracking & Optimization
                </h3>
                <p className="mt-2 text-base leading-7 text-gray-600">
                  Track and optimize all your casino bonuses, free spins, and promotional offers. Ensure expired promotions don't appear in search results.
                </p>
              </div>
              <div className="flex flex-col">
                <h3 className="text-lg font-semibold leading-8 tracking-tight text-gray-900">
                  🏆 Tournament & Event SEO
                </h3>
                <p className="mt-2 text-base leading-7 text-gray-600">
                  Optimize casino tournaments, leaderboards, and special events for maximum visibility. Get time-sensitive content indexed immediately.
                </p>
              </div>
              <div className="flex flex-col">
                <h3 className="text-lg font-semibold leading-8 tracking-tight text-gray-900">
                  📱 Mobile Casino Optimization
                </h3>
                <p className="mt-2 text-base leading-7 text-gray-600">
                  Ensure your mobile casino experience is perfectly optimized for search engines and social sharing, capturing the growing mobile gaming market.
                </p>
              </div>
            </div>
          </Container>
        </div>

        {/* CTA Section */}
        <div className="bg-rankrender-600 py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl text-center">
              <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl">
                Ready to optimize your casino's SEO?
              </h2>
              <p className="mx-auto mt-6 max-w-xl text-lg leading-8 text-rankrender-100">
                Join successful casino operators who use RankRender to boost their search visibility and attract more players to their games.
              </p>
              <div className="mt-10 flex items-center justify-center gap-x-6">
                <Button href="/register" color="white">
                  Start Free Trial
                </Button>
                <Button href="/contact" variant="outline" color="white">
                  Get Casino Demo
                </Button>
              </div>
            </div>
          </Container>
        </div>
      </main>
      <Footer />
    </>
  )
}
