import { type Metadata } from 'next'
import { Container } from '@/components/Container'
import { Header } from '@/components/Header'
import { Footer } from '@/components/Footer'
import { Button } from '@/components/Button'

export const metadata: Metadata = {
  title: 'Poker Platform SEO Solutions',
  description: 'Specialized SEO optimization for online poker platforms. Enhance tournament and cash game visibility.',
}

export default function PokerPlatforms() {
  return (
    <>
      <Header />
      <main>
        {/* Hero Section */}
        <div className="bg-white py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl text-center">
              <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl">
                Poker Platform SEO Solutions
              </h1>
              <p className="mt-6 text-lg leading-8 text-gray-600">
                Specialized SEO optimization for online poker platforms. Enhance tournament and cash game visibility 
                to attract more players to your poker room.
              </p>
              <div className="mt-10 flex items-center justify-center gap-x-6">
                <Button href="/register">Start Free Trial</Button>
                <Button href="/contact" variant="outline">
                  Get Poker Demo
                </Button>
              </div>
            </div>
          </Container>
        </div>

        {/* Poker SEO Challenges */}
        <div className="bg-gray-50 py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl lg:text-center">
              <h2 className="text-base font-semibold leading-7 text-rankrender-600">Poker SEO challenges</h2>
              <p className="mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                Unique challenges for poker platforms
              </p>
              <p className="mt-6 text-lg leading-8 text-gray-600">
                Poker platforms face specific SEO challenges related to tournament schedules, player dynamics, and game variations.
              </p>
            </div>
            <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
              <dl className="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-2">
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-red-600">
                    <span className="text-2xl">🏆</span>
                    Tournament Schedule Invisibility
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      Poker tournaments and their schedules are often dynamically generated, making them invisible to search engines 
                      and missing valuable traffic from players searching for specific tournaments.
                    </p>
                  </dd>
                </div>
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-red-600">
                    <span className="text-2xl">💰</span>
                    Cash Game Tracking Issues
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      Live cash game tables, stakes, and availability change constantly but search engines can't track these updates, 
                      resulting in outdated information in search results.
                    </p>
                  </dd>
                </div>
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-red-600">
                    <span className="text-2xl">👥</span>
                    Player Community Content
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      User-generated content like hand histories, strategy discussions, and player profiles are often 
                      JavaScript-heavy and not properly indexed by search engines.
                    </p>
                  </dd>
                </div>
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-red-600">
                    <span className="text-2xl">🎯</span>
                    Game Variant Complexity
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      Different poker variants (Texas Hold'em, Omaha, Stud, etc.) and their specific rules and strategies 
                      create complex content structures that are difficult to optimize effectively.
                    </p>
                  </dd>
                </div>
              </dl>
            </div>
          </Container>
        </div>

        {/* RankRender Solutions */}
        <div className="bg-white py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl lg:text-center">
              <h2 className="text-base font-semibold leading-7 text-rankrender-600">RankRender solutions</h2>
              <p className="mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                How we optimize poker platforms
              </p>
              <p className="mt-6 text-lg leading-8 text-gray-600">
                Our specialized poker optimization ensures every tournament, cash game, and community feature is discoverable.
              </p>
            </div>
            <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
              <dl className="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-2">
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-green-600">
                    <svg className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Tournament Schedule Optimization
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      Poker tournaments and schedules are automatically tracked and optimized, ensuring search engines 
                      can discover upcoming events, buy-ins, prize pools, and tournament structures.
                    </p>
                  </dd>
                </div>
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-green-600">
                    <svg className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Real-Time Cash Game Tracking
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      Live cash game tables, stakes, and seat availability are optimized in real-time, 
                      helping players find the right games and improving your platform's search visibility.
                    </p>
                  </dd>
                </div>
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-green-600">
                    <svg className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Community Content Indexing
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      User-generated content including strategy discussions, hand analyses, and player profiles 
                      are properly indexed, creating valuable long-tail search opportunities.
                    </p>
                  </dd>
                </div>
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-green-600">
                    <svg className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Game Variant Optimization
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      Different poker variants and their rules are optimized with proper categorization and structured data, 
                      helping players find their preferred games and improving search rankings.
                    </p>
                  </dd>
                </div>
              </dl>
            </div>
          </Container>
        </div>

        {/* Poker Game Types */}
        <div className="bg-gray-50 py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl lg:text-center">
              <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                Optimize every poker variant
              </h2>
              <p className="mt-6 text-lg leading-8 text-gray-600">
                Comprehensive optimization for all poker games and tournament formats.
              </p>
            </div>
            <div className="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-x-8 gap-y-16 sm:mt-20 lg:mx-0 lg:max-w-none lg:grid-cols-3">
              <div className="flex flex-col items-start">
                <div className="flex items-center gap-x-3">
                  <span className="text-3xl">🃏</span>
                  <h3 className="text-lg font-semibold leading-8 tracking-tight text-gray-900">
                    Texas Hold'em
                  </h3>
                </div>
                <p className="mt-4 text-base leading-7 text-gray-600">
                  Optimize No-Limit, Pot-Limit, and Fixed-Limit Hold'em games with proper stake categorization and tournament structures.
                </p>
                <ul className="mt-4 space-y-2 text-sm text-gray-600">
                  <li>• Cash game stake levels</li>
                  <li>• Tournament buy-in ranges</li>
                  <li>• Sit & Go variations</li>
                  <li>• Multi-table tournaments</li>
                </ul>
              </div>
              <div className="flex flex-col items-start">
                <div className="flex items-center gap-x-3">
                  <span className="text-3xl">🎯</span>
                  <h3 className="text-lg font-semibold leading-8 tracking-tight text-gray-900">
                    Omaha & Variants
                  </h3>
                </div>
                <p className="mt-4 text-base leading-7 text-gray-600">
                  Enhance discoverability of Omaha Hi, Omaha Hi-Lo, and other Omaha variants with detailed game descriptions.
                </p>
                <ul className="mt-4 space-y-2 text-sm text-gray-600">
                  <li>• Pot-Limit Omaha (PLO)</li>
                  <li>• Omaha Hi-Lo Split</li>
                  <li>• 5-Card Omaha</li>
                  <li>• Mixed game formats</li>
                </ul>
              </div>
              <div className="flex flex-col items-start">
                <div className="flex items-center gap-x-3">
                  <span className="text-3xl">🏆</span>
                  <h3 className="text-lg font-semibold leading-8 tracking-tight text-gray-900">
                    Tournaments & Events
                  </h3>
                </div>
                <p className="mt-4 text-base leading-7 text-gray-600">
                  Optimize special tournaments, series events, and championship qualifiers for maximum player discovery.
                </p>
                <ul className="mt-4 space-y-2 text-sm text-gray-600">
                  <li>• Daily tournament schedules</li>
                  <li>• Championship series</li>
                  <li>• Satellite qualifiers</li>
                  <li>• Freeroll tournaments</li>
                </ul>
              </div>
            </div>
          </Container>
        </div>

        {/* Results & Benefits */}
        <div className="bg-white py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl lg:text-center">
              <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                Proven results for poker platforms
              </h2>
              <p className="mt-6 text-lg leading-8 text-gray-600">
                Poker platforms using RankRender see significant improvements in player acquisition and tournament participation.
              </p>
            </div>
            <div className="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-x-8 gap-y-16 sm:mt-20 lg:mx-0 lg:max-w-none lg:grid-cols-4">
              <div className="flex flex-col items-center text-center">
                <div className="text-4xl font-bold tracking-tight text-rankrender-600">
                  350%
                </div>
                <div className="text-sm leading-6 text-gray-600 mt-2">
                  Increase in tournament visibility
                </div>
                <p className="mt-4 text-sm text-gray-500">
                  Poker tournaments rank higher in search results
                </p>
              </div>
              <div className="flex flex-col items-center text-center">
                <div className="text-4xl font-bold tracking-tight text-rankrender-600">
                  80%
                </div>
                <div className="text-sm leading-6 text-gray-600 mt-2">
                  Faster schedule indexing
                </div>
                <p className="mt-4 text-sm text-gray-500">
                  Tournament schedules appear in search within hours
                </p>
              </div>
              <div className="flex flex-col items-center text-center">
                <div className="text-4xl font-bold tracking-tight text-rankrender-600">
                  220%
                </div>
                <div className="text-sm leading-6 text-gray-600 mt-2">
                  More organic player acquisition
                </div>
                <p className="mt-4 text-sm text-gray-500">
                  Increased organic traffic from poker searches
                </p>
              </div>
              <div className="flex flex-col items-center text-center">
                <div className="text-4xl font-bold tracking-tight text-rankrender-600">
                  95%
                </div>
                <div className="text-sm leading-6 text-gray-600 mt-2">
                  Community content indexed
                </div>
                <p className="mt-4 text-sm text-gray-500">
                  User-generated content properly discoverable
                </p>
              </div>
            </div>
          </Container>
        </div>

        {/* CTA Section */}
        <div className="bg-rankrender-600 py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl text-center">
              <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl">
                Ready to optimize your poker platform?
              </h2>
              <p className="mx-auto mt-6 max-w-xl text-lg leading-8 text-rankrender-100">
                Join leading poker platforms that trust RankRender to optimize their tournaments and attract more players.
              </p>
              <div className="mt-10 flex items-center justify-center gap-x-6">
                <Button href="/register" color="white">
                  Start Free Trial
                </Button>
                <Button href="/contact" variant="outline" color="white">
                  Get Poker Demo
                </Button>
              </div>
            </div>
          </Container>
        </div>
      </main>
      <Footer />
    </>
  )
}
