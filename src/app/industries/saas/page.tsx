import { type Metadata } from 'next'
import { Container } from '@/components/Container'
import { Header } from '@/components/Header'
import { Footer } from '@/components/Footer'
import { Button } from '@/components/Button'

export const metadata: Metadata = {
  title: 'SaaS Platform SEO Solutions',
  description: 'Specialized SEO optimization for SaaS platforms. Enhance software documentation and feature visibility.',
}

export default function SaasPlatforms() {
  return (
    <>
      <Header />
      <main>
        {/* Hero Section */}
        <div className="bg-white py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl text-center">
              <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl">
                SaaS Platform SEO Solutions
              </h1>
              <p className="mt-6 text-lg leading-8 text-gray-600">
                Specialized SEO optimization for SaaS platforms and software companies.
                Enhance software documentation, feature visibility, and user-generated content discovery.
              </p>
              <div className="mt-10 flex items-center justify-center gap-x-6">
                <Button href="/register">Get Started Free</Button>
                <Button href="/contact" variant="outline">
                  Get SaaS Demo
                </Button>
              </div>
            </div>
          </Container>
        </div>

        {/* SaaS SEO Challenges */}
        <div className="bg-gray-50 py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl lg:text-center">
              <h2 className="text-base font-semibold leading-7 text-rankrender-600">SaaS SEO challenges</h2>
              <p className="mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                Unique challenges for software platforms
              </p>
              <p className="mt-6 text-lg leading-8 text-gray-600">
                SaaS platforms face specific SEO challenges related to feature documentation, user-generated content, and dynamic interfaces.
              </p>
            </div>
            <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
              <dl className="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-2">
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-red-600">
                    <span className="text-2xl">📚</span>
                    Documentation Invisibility
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      Feature documentation, API guides, and help articles are often behind login walls or generated dynamically,
                      missing valuable traffic from developers and users searching for solutions.
                    </p>
                  </dd>
                </div>
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-red-600">
                    <span className="text-2xl">⚙️</span>
                    Feature Discovery Issues
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      Software features, integrations, and capabilities are JavaScript-heavy and not properly indexed,
                      preventing potential customers from discovering your platform's full functionality.
                    </p>
                  </dd>
                </div>
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-red-600">
                    <span className="text-2xl">👥</span>
                    User Content Isolation
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      User-generated content like community discussions, templates, and shared workflows are typically
                      in dashboards that search engines can't access, limiting organic discovery.
                    </p>
                  </dd>
                </div>
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-red-600">
                    <span className="text-2xl">🔗</span>
                    Integration Complexity
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      Third-party integrations, API endpoints, and connector documentation create complex content structures
                      that are difficult to organize and optimize for search engines.
                    </p>
                  </dd>
                </div>
              </dl>
            </div>
          </Container>
        </div>

        {/* RankRender Solutions */}
        <div className="bg-white py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl lg:text-center">
              <h2 className="text-base font-semibold leading-7 text-rankrender-600">RankRender solutions</h2>
              <p className="mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                How we optimize SaaS platforms
              </p>
              <p className="mt-6 text-lg leading-8 text-gray-600">
                Our specialized SaaS optimization ensures every feature, document, and user interaction is discoverable.
              </p>
            </div>
            <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
              <dl className="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-2">
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-green-600">
                    <svg className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Complete Documentation Indexing
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      Feature documentation, API guides, tutorials, and help articles are properly indexed with structured data,
                      making them discoverable to developers and users searching for solutions.
                    </p>
                  </dd>
                </div>
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-green-600">
                    <svg className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Feature Visibility Optimization
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      Software features, integrations, and capabilities are optimized for search discovery,
                      helping potential customers understand your platform's full functionality and use cases.
                    </p>
                  </dd>
                </div>
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-green-600">
                    <svg className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    User Content Discovery
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      Community discussions, user templates, and shared workflows are made searchable while respecting privacy,
                      creating valuable long-tail search opportunities and social proof.
                    </p>
                  </dd>
                </div>
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-green-600">
                    <svg className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Integration SEO
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      Third-party integrations and API documentation are organized with proper categorization and structured data,
                      making it easy for developers to find and implement your platform's integrations.
                    </p>
                  </dd>
                </div>
              </dl>
            </div>
          </Container>
        </div>

        {/* SaaS Content Types */}
        <div className="bg-gray-50 py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl lg:text-center">
              <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                Optimize every SaaS content type
              </h2>
              <p className="mt-6 text-lg leading-8 text-gray-600">
                Comprehensive optimization for all aspects of your software platform and user experience.
              </p>
            </div>
            <div className="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-x-8 gap-y-16 sm:mt-20 lg:mx-0 lg:max-w-none lg:grid-cols-3">
              <div className="flex flex-col items-start">
                <div className="flex items-center gap-x-3">
                  <span className="text-3xl">📖</span>
                  <h3 className="text-lg font-semibold leading-8 tracking-tight text-gray-900">
                    Documentation & Guides
                  </h3>
                </div>
                <p className="mt-4 text-base leading-7 text-gray-600">
                  Optimize feature documentation, API guides, tutorials, and help articles for developer and user discovery.
                </p>
                <ul className="mt-4 space-y-2 text-sm text-gray-600">
                  <li>• API documentation SEO</li>
                  <li>• Tutorial and guide optimization</li>
                  <li>• Help article indexing</li>
                  <li>• Code example visibility</li>
                </ul>
              </div>
              <div className="flex flex-col items-start">
                <div className="flex items-center gap-x-3">
                  <span className="text-3xl">⚙️</span>
                  <h3 className="text-lg font-semibold leading-8 tracking-tight text-gray-900">
                    Features & Integrations
                  </h3>
                </div>
                <p className="mt-4 text-base leading-7 text-gray-600">
                  Enhance discoverability of software features, third-party integrations, and platform capabilities.
                </p>
                <ul className="mt-4 space-y-2 text-sm text-gray-600">
                  <li>• Feature page optimization</li>
                  <li>• Integration marketplace SEO</li>
                  <li>• Use case documentation</li>
                  <li>• Workflow examples</li>
                </ul>
              </div>
              <div className="flex flex-col items-start">
                <div className="flex items-center gap-x-3">
                  <span className="text-3xl">👥</span>
                  <h3 className="text-lg font-semibold leading-8 tracking-tight text-gray-900">
                    Community & Support
                  </h3>
                </div>
                <p className="mt-4 text-base leading-7 text-gray-600">
                  Optimize community content, user forums, and support resources for better customer discovery.
                </p>
                <ul className="mt-4 space-y-2 text-sm text-gray-600">
                  <li>• Community forum SEO</li>
                  <li>• User template galleries</li>
                  <li>• Support article optimization</li>
                  <li>• Customer success stories</li>
                </ul>
              </div>
            </div>
          </Container>
        </div>

        {/* Results & Benefits */}
        <div className="bg-white py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl lg:text-center">
              <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                Proven results for SaaS platforms
              </h2>
              <p className="mt-6 text-lg leading-8 text-gray-600">
                SaaS companies using RankRender see significant improvements in feature discovery and user acquisition.
              </p>
            </div>
            <div className="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-x-8 gap-y-16 sm:mt-20 lg:mx-0 lg:max-w-none lg:grid-cols-4">
              <div className="flex flex-col items-center text-center">
                <div className="text-4xl font-bold tracking-tight text-rankrender-600">
                  290%
                </div>
                <div className="text-sm leading-6 text-gray-600 mt-2">
                  Increase in documentation traffic
                </div>
                <p className="mt-4 text-sm text-gray-500">
                  More developers find and use your APIs
                </p>
              </div>
              <div className="flex flex-col items-center text-center">
                <div className="text-4xl font-bold tracking-tight text-rankrender-600">
                  85%
                </div>
                <div className="text-sm leading-6 text-gray-600 mt-2">
                  Faster feature indexing
                </div>
                <p className="mt-4 text-sm text-gray-500">
                  New features appear in search quickly
                </p>
              </div>
              <div className="flex flex-col items-center text-center">
                <div className="text-4xl font-bold tracking-tight text-rankrender-600">
                  160%
                </div>
                <div className="text-sm leading-6 text-gray-600 mt-2">
                  More qualified leads
                </div>
                <p className="mt-4 text-sm text-gray-500">
                  Better targeting of potential customers
                </p>
              </div>
              <div className="flex flex-col items-center text-center">
                <div className="text-4xl font-bold tracking-tight text-rankrender-600">
                  95%
                </div>
                <div className="text-sm leading-6 text-gray-600 mt-2">
                  Documentation coverage
                </div>
                <p className="mt-4 text-sm text-gray-500">
                  All help content properly indexed
                </p>
              </div>
            </div>
          </Container>
        </div>

        {/* CTA Section */}
        <div className="bg-rankrender-600 py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl text-center">
              <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl">
                Ready to optimize your SaaS platform?
              </h2>
              <p className="mx-auto mt-6 max-w-xl text-lg leading-8 text-rankrender-100">
                Join leading SaaS companies that trust RankRender to optimize their platforms and attract more users.
              </p>
              <div className="mt-10 flex items-center justify-center gap-x-6">
                <Button href="/register" color="white">
                  Get Started Free
                </Button>
                <Button href="/contact" variant="outline" color="white">
                  Get SaaS Demo
                </Button>
              </div>
            </div>
          </Container>
        </div>
      </main>
      <Footer />
    </>
  )
}
