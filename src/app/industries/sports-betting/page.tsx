import { type Metadata } from 'next'
import { Container } from '@/components/Container'
import { Header } from '@/components/Header'
import { Footer } from '@/components/Footer'
import { Button } from '@/components/Button'

export const metadata: Metadata = {
  title: 'Sports Betting SEO Solutions',
  description: 'Specialized SEO optimization for sports betting platforms. Optimize odds pages, live betting interfaces, and sporting events.',
}

export default function SportsBetting() {
  return (
    <>
      <Header />
      <main>
        {/* Hero Section */}
        <div className="bg-white py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl text-center">
              <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl">
                Sports Betting SEO Solutions
              </h1>
              <p className="mt-6 text-lg leading-8 text-gray-600">
                Specialized SEO optimization for sports betting platforms. Optimize odds pages,
                live betting interfaces, and sporting events for maximum search visibility.
              </p>
              <div className="mt-10 flex items-center justify-center gap-x-6">
                <Button href="/register">Get Started Free</Button>
                <Button href="/contact" variant="outline">
                  Get Sportsbook Demo
                </Button>
              </div>
            </div>
          </Container>
        </div>

        {/* Sports Betting Challenges */}
        <div className="bg-gray-50 py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl lg:text-center">
              <h2 className="text-base font-semibold leading-7 text-rankrender-600">Sportsbook SEO challenges</h2>
              <p className="mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                Unique challenges for sports betting platforms
              </p>
              <p className="mt-6 text-lg leading-8 text-gray-600">
                Sports betting platforms face dynamic content challenges that traditional SEO tools can't handle effectively.
              </p>
            </div>
            <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
              <dl className="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-2">
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-red-600">
                    <span className="text-2xl">📊</span>
                    Dynamic Odds Pages
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      Sports betting odds change constantly throughout the day, making it impossible for search engines
                      to properly index and rank odds pages for specific games and events.
                    </p>
                  </dd>
                </div>
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-red-600">
                    <span className="text-2xl">⚡</span>
                    Live Betting Invisibility
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      Live betting interfaces and in-play odds are heavily JavaScript-dependent,
                      making them invisible to search engines and missing valuable traffic from live betting searches.
                    </p>
                  </dd>
                </div>
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-red-600">
                    <span className="text-2xl">🏆</span>
                    Event Coverage Gaps
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      Major sporting events, tournaments, and seasonal competitions aren't properly indexed,
                      missing out on high-volume search traffic during peak betting periods.
                    </p>
                  </dd>
                </div>
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-red-600">
                    <span className="text-2xl">🌍</span>
                    Multi-Market Complexity
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      Different sports markets, bet types, and regional preferences create complex content structures
                      that are difficult to optimize for search engines across multiple jurisdictions.
                    </p>
                  </dd>
                </div>
              </dl>
            </div>
          </Container>
        </div>

        {/* RankRender Solutions */}
        <div className="bg-white py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl lg:text-center">
              <h2 className="text-base font-semibold leading-7 text-rankrender-600">RankRender solutions</h2>
              <p className="mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                How we optimize sports betting platforms
              </p>
              <p className="mt-6 text-lg leading-8 text-gray-600">
                Our specialized sportsbook optimization ensures every game, market, and betting option is discoverable.
              </p>
            </div>
            <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
              <dl className="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-2">
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-green-600">
                    <svg className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Real-Time Odds Optimization
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      Sports betting odds and markets are automatically optimized as they change, ensuring search engines
                      can discover and rank your sportsbook for live events and upcoming games.
                    </p>
                  </dd>
                </div>
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-green-600">
                    <svg className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Live Betting Interface SEO
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      In-play betting features and live odds are made accessible to search engines,
                      capturing valuable traffic from users searching for live betting opportunities.
                    </p>
                  </dd>
                </div>
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-green-600">
                    <svg className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Event & Tournament Coverage
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      Major sporting events, tournaments, and seasonal competitions are automatically tracked and optimized,
                      ensuring maximum visibility during peak betting periods.
                    </p>
                  </dd>
                </div>
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-green-600">
                    <svg className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Multi-Market Optimization
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      Different sports markets, bet types, and regional preferences are optimized for local search patterns,
                      ensuring relevance across multiple jurisdictions and sports categories.
                    </p>
                  </dd>
                </div>
              </dl>
            </div>
          </Container>
        </div>

        {/* Sports Categories */}
        <div className="bg-gray-50 py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl lg:text-center">
              <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                Optimize every sport and market
              </h2>
              <p className="mt-6 text-lg leading-8 text-gray-600">
                Comprehensive optimization for all major sports and betting markets.
              </p>
            </div>
            <div className="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-x-8 gap-y-16 sm:mt-20 lg:mx-0 lg:max-w-none lg:grid-cols-3">
              <div className="flex flex-col items-start">
                <div className="flex items-center gap-x-3">
                  <span className="text-3xl">⚽</span>
                  <h3 className="text-lg font-semibold leading-8 tracking-tight text-gray-900">
                    Football & Soccer
                  </h3>
                </div>
                <p className="mt-4 text-base leading-7 text-gray-600">
                  Optimize NFL, college football, Premier League, Champions League, and international soccer betting markets.
                </p>
                <ul className="mt-4 space-y-2 text-sm text-gray-600">
                  <li>• Match result and handicap betting</li>
                  <li>• Player props and statistics</li>
                  <li>• Tournament and league futures</li>
                  <li>• Live in-play betting markets</li>
                </ul>
              </div>
              <div className="flex flex-col items-start">
                <div className="flex items-center gap-x-3">
                  <span className="text-3xl">🏀</span>
                  <h3 className="text-lg font-semibold leading-8 tracking-tight text-gray-900">
                    Basketball & More
                  </h3>
                </div>
                <p className="mt-4 text-base leading-7 text-gray-600">
                  NBA, college basketball, tennis, baseball, hockey, and other major sports betting optimization.
                </p>
                <ul className="mt-4 space-y-2 text-sm text-gray-600">
                  <li>• Point spreads and totals</li>
                  <li>• Player performance betting</li>
                  <li>• Season-long futures markets</li>
                  <li>• Playoff and championship odds</li>
                </ul>
              </div>
              <div className="flex flex-col items-start">
                <div className="flex items-center gap-x-3">
                  <span className="text-3xl">🏇</span>
                  <h3 className="text-lg font-semibold leading-8 tracking-tight text-gray-900">
                    Racing & Esports
                  </h3>
                </div>
                <p className="mt-4 text-base leading-7 text-gray-600">
                  Horse racing, greyhound racing, motor sports, and esports betting market optimization.
                </p>
                <ul className="mt-4 space-y-2 text-sm text-gray-600">
                  <li>• Race cards and form guides</li>
                  <li>• Esports tournament betting</li>
                  <li>• Virtual sports markets</li>
                  <li>• Special event wagering</li>
                </ul>
              </div>
            </div>
          </Container>
        </div>

        {/* Results & Benefits */}
        <div className="bg-white py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl lg:text-center">
              <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                Proven results for sportsbooks
              </h2>
              <p className="mt-6 text-lg leading-8 text-gray-600">
                Sports betting platforms using RankRender see significant improvements in search visibility and player acquisition.
              </p>
            </div>
            <div className="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-x-8 gap-y-16 sm:mt-20 lg:mx-0 lg:max-w-none lg:grid-cols-4">
              <div className="flex flex-col items-center text-center">
                <div className="text-4xl font-bold tracking-tight text-rankrender-600">
                  400%
                </div>
                <div className="text-sm leading-6 text-gray-600 mt-2">
                  Increase in event page visibility
                </div>
                <p className="mt-4 text-sm text-gray-500">
                  Major sporting events rank higher in search results
                </p>
              </div>
              <div className="flex flex-col items-center text-center">
                <div className="text-4xl font-bold tracking-tight text-rankrender-600">
                  90%
                </div>
                <div className="text-sm leading-6 text-gray-600 mt-2">
                  Faster odds page indexing
                </div>
                <p className="mt-4 text-sm text-gray-500">
                  Live odds and markets appear in search within minutes
                </p>
              </div>
              <div className="flex flex-col items-center text-center">
                <div className="text-4xl font-bold tracking-tight text-rankrender-600">
                  250%
                </div>
                <div className="text-sm leading-6 text-gray-600 mt-2">
                  More organic traffic
                </div>
                <p className="mt-4 text-sm text-gray-500">
                  Increased organic traffic from sports betting searches
                </p>
              </div>
              <div className="flex flex-col items-center text-center">
                <div className="text-4xl font-bold tracking-tight text-rankrender-600">
                  100%
                </div>
                <div className="text-sm leading-6 text-gray-600 mt-2">
                  Regulatory compliance
                </div>
                <p className="mt-4 text-sm text-gray-500">
                  All optimizations meet sports betting regulations
                </p>
              </div>
            </div>
          </Container>
        </div>

        {/* CTA Section */}
        <div className="bg-rankrender-600 py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl text-center">
              <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl">
                Ready to optimize your sportsbook?
              </h2>
              <p className="mx-auto mt-6 max-w-xl text-lg leading-8 text-rankrender-100">
                Join leading sports betting platforms that trust RankRender to optimize their odds and attract more players.
              </p>
              <div className="mt-10 flex items-center justify-center gap-x-6">
                <Button href="/register" color="white">
                  Get Started Free
                </Button>
                <Button href="/contact" variant="outline" color="white">
                  Get Sportsbook Demo
                </Button>
              </div>
            </div>
          </Container>
        </div>
      </main>
      <Footer />
    </>
  )
}
