import { type Metadata } from 'next'
import { Container } from '@/components/Container'
import { Header } from '@/components/Header'
import { Footer } from '@/components/Footer'
import { Button } from '@/components/Button'

export const metadata: Metadata = {
  title: 'Pricing',
  description: 'Simple, transparent pricing for iGaming platforms of all sizes.',
}

const tiers = [
  {
    name: 'Free',
    id: 'tier-free',
    href: '/register',
    priceMonthly: '$0',
    description: 'Perfect for testing RankRender with small iGaming sites.',
    features: [
      'Up to 1,000 page renders/month',
      'Basic SEO optimization',
      'Social media link previews',
      '3-day cache freshness',
      'Knowledge base access',
      'Community support',
    ],
    mostPopular: false,
    isFree: true,
  },
  {
    name: 'Starter',
    id: 'tier-starter',
    href: '/register',
    priceMonthly: '$99',
    description: 'Perfect for small iGaming sites and new operators.',
    features: [
      'Up to 10,000 page renders/month',
      'Advanced SEO optimization',
      'Social media link previews',
      '24-hour cache freshness',
      'Email support',
      'Basic analytics dashboard',
      'iGaming compliance checks',
    ],
    mostPopular: false,
    isFree: false,
  },
  {
    name: 'Professional',
    id: 'tier-professional',
    href: '/register',
    priceMonthly: '$299',
    description: 'Ideal for growing casino and betting platforms.',
    features: [
      'Up to 100,000 page renders/month',
      'Premium SEO optimization',
      'AI search engine visibility',
      'Priority support',
      'Advanced analytics & reporting',
      'Custom caching rules',
      'Compliance monitoring',
      'API access',
    ],
    mostPopular: true,
    isFree: false,
  },
  {
    name: 'Enterprise',
    id: 'tier-enterprise',
    href: '/contact',
    priceMonthly: 'Custom',
    description: 'For large-scale iGaming operations and networks.',
    features: [
      'Unlimited page renders',
      'White-label solution',
      'Dedicated account manager',
      '24/7 phone support',
      'Custom integrations',
      'SLA guarantees',
      'Multi-brand management',
      'Advanced compliance tools',
    ],
    mostPopular: false,
    isFree: false,
  },
]

function CheckIcon(props: React.ComponentPropsWithoutRef<'svg'>) {
  return (
    <svg viewBox="0 0 24 24" aria-hidden="true" {...props}>
      <path
        d="m9.307 12.248 1.414 1.414L15.414 9"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

export default function Pricing() {
  return (
    <>
      <Header />
      <main>
        <div className="bg-white py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-4xl text-center">
              <h2 className="text-base font-semibold leading-7 text-rankrender-600">Pricing</h2>
              <p className="mt-2 text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl">
                Simple pricing for iGaming platforms
              </p>
            </div>
            <p className="mx-auto mt-6 max-w-2xl text-center text-lg leading-8 text-gray-600">
              Choose the perfect plan for your casino, sportsbook, or poker platform. All plans include our core SEO optimization features.
            </p>
            <div className="isolate mx-auto mt-16 grid max-w-md grid-cols-1 gap-y-8 sm:mt-20 lg:mx-0 lg:max-w-none lg:grid-cols-4 lg:gap-x-6 xl:gap-x-8">
              {tiers.map((tier) => (
                <div
                  key={tier.id}
                  className={`rounded-3xl p-8 ring-1 ${
                    tier.mostPopular
                      ? 'bg-gray-900 ring-gray-900'
                      : tier.isFree
                      ? 'bg-green-50 ring-green-200'
                      : 'ring-gray-200'
                  } xl:p-10`}
                >
                  <div className="flex items-center justify-between gap-x-4">
                    <h3
                      id={tier.id}
                      className={`text-lg font-semibold leading-8 ${
                        tier.mostPopular ? 'text-white' : 'text-gray-900'
                      }`}
                    >
                      {tier.name}
                    </h3>
                    {tier.mostPopular ? (
                      <p className="rounded-full bg-rankrender-600/10 px-2.5 py-1 text-xs font-semibold leading-5 text-rankrender-400">
                        Most popular
                      </p>
                    ) : tier.isFree ? (
                      <p className="rounded-full bg-green-600/10 px-2.5 py-1 text-xs font-semibold leading-5 text-green-600">
                        Free forever
                      </p>
                    ) : null}
                  </div>
                  <p className={`mt-4 text-sm leading-6 ${tier.mostPopular ? 'text-gray-300' : 'text-gray-600'}`}>
                    {tier.description}
                  </p>
                  <p className="mt-6 flex items-baseline gap-x-1">
                    <span
                      className={`text-4xl font-bold tracking-tight ${
                        tier.mostPopular ? 'text-white' : 'text-gray-900'
                      }`}
                    >
                      {tier.priceMonthly}
                    </span>
                    {tier.priceMonthly !== 'Custom' && (
                      <span className={`text-sm font-semibold leading-6 ${tier.mostPopular ? 'text-gray-300' : 'text-gray-600'}`}>
                        /month
                      </span>
                    )}
                  </p>
                  <Button
                    href={tier.href}
                    variant={tier.mostPopular ? 'solid' : tier.isFree ? 'solid' : 'outline'}
                    color={tier.mostPopular ? 'white' : tier.isFree ? 'green' : 'rankrender'}
                    className="mt-6 w-full"
                    aria-describedby={tier.id}
                  >
                    {tier.name === 'Enterprise' ? 'Contact sales' : tier.isFree ? 'Start free trial' : 'Get started today'}
                  </Button>
                  <ul
                    role="list"
                    className={`mt-8 space-y-3 text-sm leading-6 ${
                      tier.mostPopular ? 'text-gray-300' : 'text-gray-600'
                    }`}
                  >
                    {tier.features.map((feature) => (
                      <li key={feature} className="flex gap-x-3">
                        <CheckIcon
                          className={`h-6 w-5 flex-none ${
                            tier.mostPopular ? 'text-white' : 'text-rankrender-600'
                          }`}
                          aria-hidden="true"
                        />
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </Container>
        </div>

        {/* Free Tier Highlight */}
        <div className="bg-green-50 py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl text-center">
              <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                Start optimizing for free
              </h2>
              <p className="mt-6 text-lg leading-8 text-gray-600">
                Test RankRender with your iGaming platform at no cost. Perfect for small sites or evaluating our service before upgrading.
              </p>
              <div className="mt-10 flex items-center justify-center gap-x-6">
                <Button href="/register" color="green">
                  Start Free Trial
                </Button>
                <Button href="/contact" variant="outline" color="green">
                  Talk to Sales
                </Button>
              </div>
            </div>
            <div className="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-x-8 gap-y-16 sm:mt-20 lg:mx-0 lg:max-w-none lg:grid-cols-3">
              <div className="flex flex-col items-center text-center">
                <div className="flex h-16 w-16 items-center justify-center rounded-full bg-green-600 text-white text-xl font-bold">
                  ✓
                </div>
                <h3 className="mt-6 text-lg font-semibold leading-8 text-gray-900">
                  No Credit Card Required
                </h3>
                <p className="mt-2 text-base leading-7 text-gray-600">
                  Get started immediately with our free tier. No payment information needed to begin optimizing your iGaming site.
                </p>
              </div>
              <div className="flex flex-col items-center text-center">
                <div className="flex h-16 w-16 items-center justify-center rounded-full bg-green-600 text-white text-xl font-bold">
                  1K
                </div>
                <h3 className="mt-6 text-lg font-semibold leading-8 text-gray-900">
                  1,000 Free Renders
                </h3>
                <p className="mt-2 text-base leading-7 text-gray-600">
                  Enough to test your casino games, sports betting pages, or poker tournaments and see real SEO improvements.
                </p>
              </div>
              <div className="flex flex-col items-center text-center">
                <div className="flex h-16 w-16 items-center justify-center rounded-full bg-green-600 text-white text-xl font-bold">
                  ∞
                </div>
                <h3 className="mt-6 text-lg font-semibold leading-8 text-gray-900">
                  Free Forever
                </h3>
                <p className="mt-2 text-base leading-7 text-gray-600">
                  Keep using the free tier as long as you need. Upgrade only when you're ready to scale your SEO optimization.
                </p>
              </div>
            </div>
          </Container>
        </div>

        {/* FAQ Section */}
        <div className="bg-gray-50 py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-4xl">
              <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                Frequently asked questions
              </h2>
              <dl className="mt-16 space-y-8">
                <div>
                  <dt className="text-lg font-semibold leading-7 text-gray-900">
                    Is the free tier really free forever?
                  </dt>
                  <dd className="mt-2 text-base leading-7 text-gray-600">
                    Yes! Our free tier includes 1,000 page renders per month with no time limit and no credit card required. It's perfect for testing RankRender with your iGaming site.
                  </dd>
                </div>
                <div>
                  <dt className="text-lg font-semibold leading-7 text-gray-900">
                    What counts as a page render?
                  </dt>
                  <dd className="mt-2 text-base leading-7 text-gray-600">
                    A page render occurs when a search engine bot or social media crawler requests a page from your site. We optimize and cache these renders to improve your SEO performance.
                  </dd>
                </div>
                <div>
                  <dt className="text-lg font-semibold leading-7 text-gray-900">
                    Do you support all iGaming platforms?
                  </dt>
                  <dd className="mt-2 text-base leading-7 text-gray-600">
                    Yes, RankRender works with all major iGaming platforms including custom-built solutions, white-label platforms, and popular frameworks like React, Angular, and Vue.js.
                  </dd>
                </div>
                <div>
                  <dt className="text-lg font-semibold leading-7 text-gray-900">
                    How quickly will I see results?
                  </dt>
                  <dd className="mt-2 text-base leading-7 text-gray-600">
                    Most clients see improvements in search engine crawling within 24-48 hours of implementation. Full SEO benefits typically become visible within 2-4 weeks.
                  </dd>
                </div>
                <div>
                  <dt className="text-lg font-semibold leading-7 text-gray-900">
                    Is RankRender compliant with gambling regulations?
                  </dt>
                  <dd className="mt-2 text-base leading-7 text-gray-600">
                    Yes, we understand the complex regulatory environment of iGaming and ensure our service complies with major gambling jurisdictions including UK, Malta, and Curacao licensing requirements.
                  </dd>
                </div>
                <div>
                  <dt className="text-lg font-semibold leading-7 text-gray-900">
                    Can I upgrade or downgrade my plan anytime?
                  </dt>
                  <dd className="mt-2 text-base leading-7 text-gray-600">
                    Absolutely! You can upgrade from the free tier to any paid plan instantly. Downgrades take effect at the end of your current billing cycle.
                  </dd>
                </div>
              </dl>
            </div>
          </Container>
        </div>
      </main>
      <Footer />
    </>
  )
}
