import { type Metadata } from 'next'
import { Container } from '@/components/Container'
import { Header } from '@/components/Header'
import { Footer } from '@/components/Footer'
import { Button } from '@/components/Button'

export const metadata: Metadata = {
  title: 'Start Your Free Trial - RankRender',
  description: 'Start your free 14-day trial of RankRender. No credit card required. Full access to all JavaScript SEO optimization features.',
}

export default function Register() {
  return (
    <>
      <Header />
      <main>
        {/* Hero Section */}
        <div className="bg-white py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl text-center">
              <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl">
                Start Your Free Trial
              </h1>
              <p className="mt-6 text-lg leading-8 text-gray-600">
                Get full access to RankRender's JavaScript SEO optimization platform. 
                14 days free, no credit card required.
              </p>
              <div className="mt-8 flex items-center justify-center gap-x-4 text-sm text-gray-600">
                <div className="flex items-center">
                  <svg className="mr-2 h-4 w-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clipRule="evenodd" />
                  </svg>
                  14-day free trial
                </div>
                <div className="flex items-center">
                  <svg className="mr-2 h-4 w-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clipRule="evenodd" />
                  </svg>
                  No credit card required
                </div>
                <div className="flex items-center">
                  <svg className="mr-2 h-4 w-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clipRule="evenodd" />
                  </svg>
                  Cancel anytime
                </div>
              </div>
            </div>
          </Container>
        </div>

        {/* Registration Form */}
        <div className="bg-gray-50 py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-md">
              <div className="bg-white rounded-lg shadow-lg p-8">
                <div className="text-center mb-8">
                  <h2 className="text-2xl font-bold text-gray-900">Create Your Account</h2>
                  <p className="mt-2 text-gray-600">Start optimizing your JavaScript SEO today</p>
                </div>
                
                <form className="space-y-6">
                  <div>
                    <label htmlFor="email" className="block text-sm font-medium leading-6 text-gray-900">
                      Work Email
                    </label>
                    <div className="mt-2">
                      <input
                        type="email"
                        name="email"
                        id="email"
                        autoComplete="email"
                        required
                        className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-rankrender-600 sm:text-sm sm:leading-6"
                        placeholder="<EMAIL>"
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label htmlFor="password" className="block text-sm font-medium leading-6 text-gray-900">
                      Password
                    </label>
                    <div className="mt-2">
                      <input
                        type="password"
                        name="password"
                        id="password"
                        autoComplete="new-password"
                        required
                        className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-rankrender-600 sm:text-sm sm:leading-6"
                        placeholder="Create a strong password"
                      />
                    </div>
                    <p className="mt-1 text-xs text-gray-500">Must be at least 8 characters</p>
                  </div>
                  
                  <div>
                    <label htmlFor="company" className="block text-sm font-medium leading-6 text-gray-900">
                      Company Name
                    </label>
                    <div className="mt-2">
                      <input
                        type="text"
                        name="company"
                        id="company"
                        autoComplete="organization"
                        required
                        className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-rankrender-600 sm:text-sm sm:leading-6"
                        placeholder="Your Company"
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label htmlFor="website" className="block text-sm font-medium leading-6 text-gray-900">
                      Website URL
                    </label>
                    <div className="mt-2">
                      <input
                        type="url"
                        name="website"
                        id="website"
                        required
                        className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-rankrender-600 sm:text-sm sm:leading-6"
                        placeholder="https://yourwebsite.com"
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label htmlFor="industry" className="block text-sm font-medium leading-6 text-gray-900">
                      Industry
                    </label>
                    <div className="mt-2">
                      <select
                        id="industry"
                        name="industry"
                        required
                        className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-rankrender-600 sm:text-sm sm:leading-6"
                      >
                        <option value="">Select your industry</option>
                        <option value="igaming-casino">iGaming - Online Casino</option>
                        <option value="igaming-sportsbook">iGaming - Sports Betting</option>
                        <option value="igaming-poker">iGaming - Poker</option>
                        <option value="igaming-affiliate">iGaming - Affiliate Network</option>
                        <option value="ecommerce">E-commerce</option>
                        <option value="saas">SaaS</option>
                        <option value="fintech">Financial Services</option>
                        <option value="travel">Travel & Hospitality</option>
                        <option value="media">Media & Publishing</option>
                        <option value="other">Other</option>
                      </select>
                    </div>
                  </div>
                  
                  <div className="flex items-center">
                    <input
                      id="terms"
                      name="terms"
                      type="checkbox"
                      required
                      className="h-4 w-4 rounded border-gray-300 text-rankrender-600 focus:ring-rankrender-600"
                    />
                    <label htmlFor="terms" className="ml-3 block text-sm leading-6 text-gray-900">
                      I agree to the{' '}
                      <a href="/terms" className="text-rankrender-600 hover:text-rankrender-500">
                        Terms of Service
                      </a>{' '}
                      and{' '}
                      <a href="/privacy" className="text-rankrender-600 hover:text-rankrender-500">
                        Privacy Policy
                      </a>
                    </label>
                  </div>
                  
                  <div>
                    <Button type="submit" className="w-full">
                      Start Free Trial
                    </Button>
                  </div>
                </form>
                
                <div className="mt-6 text-center">
                  <p className="text-sm text-gray-600">
                    Already have an account?{' '}
                    <a href="/login" className="text-rankrender-600 hover:text-rankrender-500 font-medium">
                      Sign in
                    </a>
                  </p>
                </div>
              </div>
            </div>
          </Container>
        </div>

        {/* What's Included */}
        <div className="bg-white py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl lg:text-center">
              <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                What's included in your free trial
              </h2>
              <p className="mt-6 text-lg leading-8 text-gray-600">
                Get full access to all RankRender features for 14 days, completely free.
              </p>
            </div>
            <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
              <dl className="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-3">
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900">
                    <svg className="h-6 w-6 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clipRule="evenodd" />
                    </svg>
                    Complete SEO Audit
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      Comprehensive analysis of your JavaScript SEO performance with detailed recommendations for improvement.
                    </p>
                  </dd>
                </div>
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900">
                    <svg className="h-6 w-6 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clipRule="evenodd" />
                    </svg>
                    Real-Time Optimization
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      Automatic optimization of your dynamic content as it changes, with real-time monitoring and alerts.
                    </p>
                  </dd>
                </div>
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900">
                    <svg className="h-6 w-6 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clipRule="evenodd" />
                    </svg>
                    Expert Support
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      Direct access to our SEO experts for guidance, best practices, and technical implementation support.
                    </p>
                  </dd>
                </div>
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900">
                    <svg className="h-6 w-6 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clipRule="evenodd" />
                    </svg>
                    Performance Analytics
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      Detailed analytics dashboard showing indexing rates, performance improvements, and ROI metrics.
                    </p>
                  </dd>
                </div>
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900">
                    <svg className="h-6 w-6 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clipRule="evenodd" />
                    </svg>
                    Industry-Specific Features
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      Specialized optimization features for your industry, including compliance monitoring for regulated sectors.
                    </p>
                  </dd>
                </div>
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900">
                    <svg className="h-6 w-6 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clipRule="evenodd" />
                    </svg>
                    AI Search Preparation
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      Future-proof your SEO with optimization for AI search engines like ChatGPT, Claude, and Perplexity.
                    </p>
                  </dd>
                </div>
              </dl>
            </div>
          </Container>
        </div>

        {/* Security & Trust */}
        <div className="bg-gray-50 py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl lg:text-center">
              <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                Enterprise-grade security
              </h2>
              <p className="mt-6 text-lg leading-8 text-gray-600">
                Your data and website security are our top priorities.
              </p>
            </div>
            <div className="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-x-8 gap-y-16 sm:mt-20 lg:mx-0 lg:max-w-none lg:grid-cols-4">
              <div className="flex flex-col items-center text-center">
                <div className="h-12 w-12 flex items-center justify-center rounded-lg bg-rankrender-100 text-rankrender-600 mb-4">
                  <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M16.5 10.5V6.75a4.5 4.5 0 10-9 0v3.75m-.75 11.25h10.5a2.25 2.25 0 002.25-2.25v-6.75a2.25 2.25 0 00-2.25-2.25H6.75a2.25 2.25 0 00-2.25 2.25v6.75a2.25 2.25 0 002.25 2.25z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-gray-900">SSL Encryption</h3>
                <p className="mt-2 text-sm text-gray-600">All data encrypted in transit and at rest</p>
              </div>
              
              <div className="flex flex-col items-center text-center">
                <div className="h-12 w-12 flex items-center justify-center rounded-lg bg-rankrender-100 text-rankrender-600 mb-4">
                  <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75L11.25 15 15 9.75m-3.75 5.25h16.5A2.25 2.25 0 0021 18.75v-1.5a2.25 2.25 0 00-2.25-2.25H5.25a2.25 2.25 0 00-2.25 2.25v1.5A2.25 2.25 0 005.25 21h16.5A2.25 2.25 0 0024 18.75v-1.5a2.25 2.25 0 00-2.25-2.25H5.25a2.25 2.25 0 00-2.25 2.25v1.5A2.25 2.25 0 005.25 21z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-gray-900">SOC 2 Compliant</h3>
                <p className="mt-2 text-sm text-gray-600">Independently audited security controls</p>
              </div>
              
              <div className="flex flex-col items-center text-center">
                <div className="h-12 w-12 flex items-center justify-center rounded-lg bg-rankrender-100 text-rankrender-600 mb-4">
                  <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M12 21a9.004 9.004 0 008.716-6.747M12 21a9.004 9.004 0 01-8.716-6.747M12 21c2.485 0 4.5-4.03 4.5-9S14.485 3 12 3s-4.5 4.03-4.5 9 2.015 9 4.5 9z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-gray-900">GDPR Ready</h3>
                <p className="mt-2 text-sm text-gray-600">Full compliance with privacy regulations</p>
              </div>
              
              <div className="flex flex-col items-center text-center">
                <div className="h-12 w-12 flex items-center justify-center rounded-lg bg-rankrender-100 text-rankrender-600 mb-4">
                  <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-gray-900">99.9% Uptime</h3>
                <p className="mt-2 text-sm text-gray-600">Reliable service with SLA guarantee</p>
              </div>
            </div>
          </Container>
        </div>

        {/* FAQ */}
        <div className="bg-white py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl lg:text-center">
              <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                Frequently asked questions
              </h2>
            </div>
            <div className="mx-auto mt-16 max-w-2xl">
              <dl className="space-y-8">
                <div>
                  <dt className="text-lg font-semibold leading-7 text-gray-900">
                    Do I need a credit card to start the free trial?
                  </dt>
                  <dd className="mt-2 text-base leading-7 text-gray-600">
                    No, you can start your 14-day free trial without providing any payment information. 
                    You'll only need to add a payment method if you decide to continue after the trial.
                  </dd>
                </div>
                
                <div>
                  <dt className="text-lg font-semibold leading-7 text-gray-900">
                    What happens after my free trial ends?
                  </dt>
                  <dd className="mt-2 text-base leading-7 text-gray-600">
                    Your trial will automatically expire after 14 days. You can choose to upgrade to a paid plan 
                    at any time during or after your trial. No automatic charges will occur.
                  </dd>
                </div>
                
                <div>
                  <dt className="text-lg font-semibold leading-7 text-gray-900">
                    Can I cancel my trial at any time?
                  </dt>
                  <dd className="mt-2 text-base leading-7 text-gray-600">
                    Yes, you can cancel your trial at any time with no obligations. Simply contact our support team 
                    or use the cancellation option in your account settings.
                  </dd>
                </div>
                
                <div>
                  <dt className="text-lg font-semibold leading-7 text-gray-900">
                    Will RankRender work with my platform?
                  </dt>
                  <dd className="mt-2 text-base leading-7 text-gray-600">
                    RankRender works with any JavaScript-heavy website or application, including React, Vue, Angular, 
                    and other modern frameworks. We support all major platforms and CMSs.
                  </dd>
                </div>
                
                <div>
                  <dt className="text-lg font-semibold leading-7 text-gray-900">
                    Do you offer technical support during the trial?
                  </dt>
                  <dd className="mt-2 text-base leading-7 text-gray-600">
                    Yes, you get full access to our technical support team during your trial. We're here to help 
                    with setup, optimization, and any questions you might have.
                  </dd>
                </div>
              </dl>
            </div>
          </Container>
        </div>
      </main>
      <Footer />
    </>
  )
}
