import { type Metadata } from 'next'
import { Container } from '@/components/Container'
import { Header } from '@/components/Header'
import { Footer } from '@/components/Footer'
import Link from 'next/link'

export const metadata: Metadata = {
  title: 'Blog',
  description: 'Latest insights on iGaming SEO, industry trends, and optimization strategies.',
}

const posts = [
  {
    id: 1,
    title: 'The Complete Guide to iGaming SEO in 2024',
    href: '#',
    description: 'Everything you need to know about optimizing your casino or betting platform for search engines in 2024.',
    date: 'Dec 16, 2024',
    datetime: '2024-12-16',
    category: { title: 'SEO Strategy', href: '#' },
    author: {
      name: '<PERSON>',
      role: 'SEO Specialist',
      href: '#',
      imageUrl: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80',
    },
  },
  {
    id: 2,
    title: 'How AI Search Engines Are Changing iGaming Discovery',
    href: '#',
    description: 'Explore how ChatGPT, <PERSON>, and other AI tools are revolutionizing how players discover casino and betting platforms.',
    date: 'Dec 12, 2024',
    datetime: '2024-12-12',
    category: { title: 'AI & Technology', href: '#' },
    author: {
      name: 'Michael Chen',
      role: 'Technical Writer',
      href: '#',
      imageUrl: 'https://images.unsplash.com/photo-1519244703995-f4e0f30006d5?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80',
    },
  },
  {
    id: 3,
    title: 'Sports Betting SEO: Optimizing Odds Pages for Maximum Visibility',
    href: '#',
    description: 'Learn how to optimize your sportsbook\'s odds pages and live betting interfaces for better search engine performance.',
    date: 'Dec 8, 2024',
    datetime: '2024-12-08',
    category: { title: 'Sports Betting', href: '#' },
    author: {
      name: 'Emma Rodriguez',
      role: 'iGaming Expert',
      href: '#',
      imageUrl: 'https://images.unsplash.com/photo-1517841905240-472988babdf9?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80',
    },
  },
  {
    id: 4,
    title: 'Casino Game SEO: Making Your Slot Library Discoverable',
    href: '#',
    description: 'Best practices for optimizing your casino\'s game library, from slot games to live dealer content.',
    date: 'Dec 5, 2024',
    datetime: '2024-12-05',
    category: { title: 'Online Casinos', href: '#' },
    author: {
      name: 'David Park',
      role: 'Casino SEO Specialist',
      href: '#',
      imageUrl: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80',
    },
  },
  {
    id: 5,
    title: 'Compliance and SEO: Navigating iGaming Regulations',
    href: '#',
    description: 'How to maintain SEO best practices while staying compliant with gambling regulations across different jurisdictions.',
    date: 'Dec 1, 2024',
    datetime: '2024-12-01',
    category: { title: 'Compliance', href: '#' },
    author: {
      name: 'Lisa Thompson',
      role: 'Compliance Expert',
      href: '#',
      imageUrl: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80',
    },
  },
  {
    id: 6,
    title: 'Mobile Casino SEO: Capturing the Growing Mobile Gaming Market',
    href: '#',
    description: 'Optimize your mobile casino experience for search engines and ensure your games are discoverable on mobile devices.',
    date: 'Nov 28, 2024',
    datetime: '2024-11-28',
    category: { title: 'Mobile Gaming', href: '#' },
    author: {
      name: 'Alex Kumar',
      role: 'Mobile SEO Expert',
      href: '#',
      imageUrl: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80',
    },
  },
]

export default function Blog() {
  return (
    <>
      <Header />
      <main>
        <div className="bg-white py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl text-center">
              <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                iGaming SEO Insights
              </h2>
              <p className="mt-2 text-lg leading-8 text-gray-600">
                Stay ahead of the curve with the latest strategies, trends, and best practices for iGaming SEO.
              </p>
            </div>
            <div className="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-x-8 gap-y-20 lg:mx-0 lg:max-w-none lg:grid-cols-3">
              {posts.map((post) => (
                <article key={post.id} className="flex flex-col items-start justify-between">
                  <div className="flex items-center gap-x-4 text-xs">
                    <time dateTime={post.datetime} className="text-gray-500">
                      {post.date}
                    </time>
                    <Link
                      href={post.category.href}
                      className="relative z-10 rounded-full bg-gray-50 px-3 py-1.5 font-medium text-gray-600 hover:bg-gray-100"
                    >
                      {post.category.title}
                    </Link>
                  </div>
                  <div className="group relative">
                    <h3 className="mt-3 text-lg font-semibold leading-6 text-gray-900 group-hover:text-gray-600">
                      <Link href={post.href}>
                        <span className="absolute inset-0" />
                        {post.title}
                      </Link>
                    </h3>
                    <p className="mt-5 line-clamp-3 text-sm leading-6 text-gray-600">{post.description}</p>
                  </div>
                  <div className="relative mt-8 flex items-center gap-x-4">
                    <img src={post.author.imageUrl} alt="" className="h-10 w-10 rounded-full bg-gray-50" />
                    <div className="text-sm leading-6">
                      <p className="font-semibold text-gray-900">
                        <Link href={post.author.href}>
                          <span className="absolute inset-0" />
                          {post.author.name}
                        </Link>
                      </p>
                      <p className="text-gray-600">{post.author.role}</p>
                    </div>
                  </div>
                </article>
              ))}
            </div>
          </Container>
        </div>

        {/* Newsletter Signup */}
        <div className="bg-gray-50 py-16 sm:py-24">
          <Container>
            <div className="mx-auto max-w-2xl text-center">
              <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                Stay updated with iGaming SEO trends
              </h2>
              <p className="mt-4 text-lg leading-8 text-gray-600">
                Get the latest insights, case studies, and optimization tips delivered to your inbox.
              </p>
              <div className="mt-6 flex max-w-md gap-x-4 mx-auto">
                <label htmlFor="email-address" className="sr-only">
                  Email address
                </label>
                <input
                  id="email-address"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  className="min-w-0 flex-auto rounded-md border-0 bg-white px-3.5 py-2 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-rankrender-600 sm:text-sm sm:leading-6"
                  placeholder="Enter your email"
                />
                <button
                  type="submit"
                  className="flex-none rounded-md bg-rankrender-600 px-3.5 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-rankrender-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-rankrender-600"
                >
                  Subscribe
                </button>
              </div>
            </div>
          </Container>
        </div>
      </main>
      <Footer />
    </>
  )
}
