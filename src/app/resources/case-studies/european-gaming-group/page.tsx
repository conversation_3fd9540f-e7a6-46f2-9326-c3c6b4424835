import { type Metadata } from 'next'
import { Container } from '@/components/Container'
import { Header } from '@/components/Header'
import { Footer } from '@/components/Footer'
import { Button } from '@/components/Button'

export const metadata: Metadata = {
  title: 'European Gaming Group Case Study - Multi-Brand SEO Success | RankRender',
  description: 'How European Gaming Group achieved 180% traffic growth across 8 brands using RankRender Enterprise multi-brand management.',
}

export default function EuropeanGamingGroupCaseStudy() {
  return (
    <>
      <Header />
      <main>
        {/* Hero Section */}
        <div className="bg-white py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-4xl">
              <div className="text-center">
                <div className="mb-6">
                  <span className="inline-flex items-center rounded-full bg-indigo-100 px-3 py-1 text-sm font-medium text-indigo-800">
                    Enterprise Multi-Brand Success
                  </span>
                </div>
                <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl">
                  European Gaming Group: Multi-Brand SEO Mastery
                </h1>
                <p className="mt-6 text-xl leading-8 text-gray-600">
                  How a major gaming conglomerate achieved 180% traffic growth across 8 brands 
                  using RankRender's Enterprise multi-brand management platform.
                </p>
              </div>
              
              {/* Key Metrics */}
              <div className="mt-16 grid grid-cols-2 gap-8 sm:grid-cols-4">
                <div className="text-center">
                  <div className="text-4xl font-bold text-rankrender-600">180%</div>
                  <div className="text-sm text-gray-600 mt-2">Average Traffic Growth</div>
                </div>
                <div className="text-center">
                  <div className="text-4xl font-bold text-rankrender-600">8</div>
                  <div className="text-sm text-gray-600 mt-2">Brands Optimized</div>
                </div>
                <div className="text-center">
                  <div className="text-4xl font-bold text-rankrender-600">15</div>
                  <div className="text-sm text-gray-600 mt-2">Markets Covered</div>
                </div>
                <div className="text-center">
                  <div className="text-4xl font-bold text-rankrender-600">4</div>
                  <div className="text-sm text-gray-600 mt-2">Months to Full Results</div>
                </div>
              </div>
            </div>
          </Container>
        </div>

        {/* Company Overview */}
        <div className="bg-gray-50 py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-4xl">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
                <div className="lg:col-span-2">
                  <h2 className="text-3xl font-bold text-gray-900 mb-6">The Multi-Brand Challenge</h2>
                  <p className="text-gray-700 mb-6">
                    European Gaming Group operates 8 distinct gaming brands across 15 European markets, 
                    including casinos, sportsbooks, and poker platforms. Each brand required unique 
                    SEO strategies while maintaining operational efficiency and compliance across 
                    different regulatory environments.
                  </p>
                  <div className="bg-red-50 p-6 rounded-lg">
                    <h3 className="font-semibold text-red-900 mb-3">Complex Challenges:</h3>
                    <ul className="space-y-2 text-red-800 text-sm">
                      <li>• 8 separate brands with different SEO needs</li>
                      <li>• 15 markets with varying regulations</li>
                      <li>• Inconsistent performance across brands</li>
                      <li>• High operational costs managing multiple tools</li>
                      <li>• Difficulty tracking cross-brand performance</li>
                    </ul>
                  </div>
                </div>
                <div className="lg:col-span-1">
                  <div className="bg-white p-6 rounded-lg shadow-sm">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Brand Portfolio</h3>
                    <dl className="space-y-3 text-sm">
                      <div>
                        <dt className="font-medium text-gray-900">Casino Brands</dt>
                        <dd className="text-gray-600">3 premium casino sites</dd>
                      </div>
                      <div>
                        <dt className="font-medium text-gray-900">Sportsbooks</dt>
                        <dd className="text-gray-600">3 sports betting platforms</dd>
                      </div>
                      <div>
                        <dt className="font-medium text-gray-900">Poker Networks</dt>
                        <dd className="text-gray-600">2 poker platforms</dd>
                      </div>
                      <div>
                        <dt className="font-medium text-gray-900">Total Pages</dt>
                        <dd className="text-gray-600">50,000+ across all brands</dd>
                      </div>
                      <div>
                        <dt className="font-medium text-gray-900">Monthly Renders</dt>
                        <dd className="text-gray-600">2M+ required</dd>
                      </div>
                    </dl>
                  </div>
                </div>
              </div>
            </div>
          </Container>
        </div>

        {/* Enterprise Solution */}
        <div className="bg-white py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-4xl">
              <h2 className="text-3xl font-bold text-gray-900 mb-8">Why Enterprise Multi-Brand Management?</h2>
              
              <div className="bg-indigo-50 p-8 rounded-lg mb-12">
                <h3 className="text-xl font-semibold text-gray-900 mb-4">Enterprise Plan Benefits for Multi-Brand Operations</h3>
                <p className="text-gray-700 mb-6">
                  European Gaming Group needed a solution that could handle massive scale while providing 
                  granular control over each brand's SEO strategy. RankRender's Enterprise plan offered 
                  the perfect combination of unlimited rendering and sophisticated multi-brand management.
                </p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-3">Multi-Brand Features:</h4>
                    <ul className="space-y-2 text-gray-700 text-sm">
                      <li>• Unified dashboard for all 8 brands</li>
                      <li>• Brand-specific optimization rules</li>
                      <li>• Cross-brand performance analytics</li>
                      <li>• Individual brand reporting</li>
                      <li>• Market-specific compliance settings</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-3">Enterprise Support:</h4>
                    <ul className="space-y-2 text-gray-700 text-sm">
                      <li>• Dedicated customer success manager</li>
                      <li>• 24/7 priority support across all brands</li>
                      <li>• Custom API integrations</li>
                      <li>• White-label reporting options</li>
                      <li>• SLA guarantees for all properties</li>
                    </ul>
                  </div>
                </div>
              </div>

              <div className="bg-white border border-gray-200 rounded-lg p-6">
                <h3 className="text-xl font-semibold text-gray-900 mb-4">Cost Efficiency Analysis</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-3">Previous Setup (Per Brand):</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">SEO Tool per brand:</span>
                        <span>$500/month</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">8 brands total:</span>
                        <span className="text-red-600">$4,000/month</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Management overhead:</span>
                        <span className="text-red-600">$2,000/month</span>
                      </div>
                      <div className="flex justify-between font-semibold border-t pt-2">
                        <span className="text-gray-900">Total monthly cost:</span>
                        <span className="text-red-600">$6,000</span>
                      </div>
                    </div>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-3">RankRender Enterprise:</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Enterprise plan:</span>
                        <span>$2,500/month</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">All 8 brands included:</span>
                        <span className="text-green-600">$0 extra</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Unified management:</span>
                        <span className="text-green-600">$0 overhead</span>
                      </div>
                      <div className="flex justify-between font-semibold border-t pt-2">
                        <span className="text-gray-900">Total monthly cost:</span>
                        <span className="text-green-600">$2,500</span>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="mt-6 p-4 bg-green-50 rounded-lg text-center">
                  <p className="text-green-800 font-semibold">Monthly Savings: $3,500 (58% cost reduction)</p>
                </div>
              </div>
            </div>
          </Container>
        </div>

        {/* Implementation */}
        <div className="bg-gray-50 py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-4xl">
              <h2 className="text-3xl font-bold text-gray-900 mb-8">Multi-Brand Implementation Strategy</h2>
              
              <div className="space-y-8">
                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">Phase 1: Unified Dashboard Setup</h3>
                  <p className="text-gray-700 mb-4">
                    All 8 brands were integrated into a single RankRender Enterprise dashboard, 
                    allowing the team to manage everything from one interface while maintaining 
                    brand-specific configurations.
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-3">Casino Brands:</h4>
                      <ul className="space-y-1 text-gray-700 text-sm">
                        <li>• royalcasino.eu</li>
                        <li>• premiumslots.com</li>
                        <li>• europecasino.net</li>
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-3">Sportsbooks & Poker:</h4>
                      <ul className="space-y-1 text-gray-700 text-sm">
                        <li>• eurobet.com</li>
                        <li>• sportsking.eu</li>
                        <li>• beteurope.net</li>
                        <li>• pokerempire.com</li>
                        <li>• europoker.net</li>
                      </ul>
                    </div>
                  </div>
                </div>

                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">Phase 2: Brand-Specific Optimization</h3>
                  <p className="text-gray-700 mb-4">
                    Each brand received customized optimization rules based on their specific 
                    content types, target markets, and regulatory requirements.
                  </p>
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <p className="text-sm text-blue-800">
                      <strong>Example:</strong> Casino brands focused on game page optimization, 
                      while sportsbooks prioritized live odds and event pages.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </Container>
        </div>

        {/* Results */}
        <div className="bg-white py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-4xl">
              <h2 className="text-3xl font-bold text-gray-900 mb-8">Multi-Brand Results</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
                <div className="bg-white p-8 rounded-lg shadow-sm border">
                  <h3 className="text-xl font-semibold text-gray-900 mb-6">Traffic Performance by Brand Type</h3>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">Casino Brands (avg)</span>
                      <span className="text-2xl font-bold text-green-600">+220%</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">Sportsbooks (avg)</span>
                      <span className="text-2xl font-bold text-green-600">+165%</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">Poker Platforms (avg)</span>
                      <span className="text-2xl font-bold text-green-600">+155%</span>
                    </div>
                  </div>
                </div>
                
                <div className="bg-white p-8 rounded-lg shadow-sm border">
                  <h3 className="text-xl font-semibold text-gray-900 mb-6">Operational Efficiency</h3>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">Management Time Saved</span>
                      <span className="text-2xl font-bold text-green-600">75%</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">Cross-Brand Insights</span>
                      <span className="text-2xl font-bold text-blue-600">Real-time</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">Compliance Monitoring</span>
                      <span className="text-2xl font-bold text-green-600">Automated</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white p-8 rounded-lg shadow-sm border">
                <h3 className="text-xl font-semibold text-gray-900 mb-6">Enterprise ROI Analysis</h3>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-indigo-600">$2,500</div>
                    <div className="text-sm text-gray-600">Monthly Enterprise Cost</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-green-600">$3,500</div>
                    <div className="text-sm text-gray-600">Monthly Savings vs Previous</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-green-600">$850K</div>
                    <div className="text-sm text-gray-600">Additional Monthly Revenue</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-green-600">340:1</div>
                    <div className="text-sm text-gray-600">ROI Ratio</div>
                  </div>
                </div>
              </div>
            </div>
          </Container>
        </div>

        {/* Testimonial */}
        <div className="bg-gray-50 py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-4xl">
              <div className="bg-white p-12 rounded-lg shadow-sm">
                <blockquote className="text-xl italic text-gray-700 mb-6">
                  "Managing SEO for 8 brands was a nightmare before RankRender. We were paying $6,000/month for multiple tools and still struggling with consistency. RankRender's Enterprise plan not only saved us $3,500 monthly but gave us a unified view of all our brands. The multi-brand dashboard is incredible - we can see performance across all properties and optimize each brand's specific needs. The 340x ROI speaks for itself."
                </blockquote>
                <div className="flex items-center">
                  <div className="h-16 w-16 rounded-full bg-gray-200 flex items-center justify-center mr-4">
                    <span className="text-xl font-semibold text-gray-600">CM</span>
                  </div>
                  <div>
                    <div className="font-semibold text-gray-900">Christina Mueller</div>
                    <div className="text-gray-600">VP of Digital Strategy, European Gaming Group</div>
                  </div>
                </div>
              </div>
            </div>
          </Container>
        </div>

        {/* CTA Section */}
        <div className="bg-rankrender-600 py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl text-center">
              <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl">
                Ready for Multi-Brand SEO Success?
              </h2>
              <p className="mx-auto mt-6 max-w-xl text-lg leading-8 text-rankrender-100">
                See how RankRender's Enterprise plan can unify and optimize your 
                multi-brand gaming portfolio with significant cost savings.
              </p>
              <div className="mt-10 flex items-center justify-center gap-x-6">
                <Button href="/contact" color="white">
                  Contact Enterprise Sales
                </Button>
                <Button href="/register" variant="outline" color="white">
                  Start Free Trial
                </Button>
              </div>
            </div>
          </Container>
        </div>
      </main>
      <Footer />
    </>
  )
}
