import { type Metadata } from 'next'
import { Container } from '@/components/Container'
import { Header } from '@/components/Header'
import { Footer } from '@/components/Footer'
import { Button } from '@/components/Button'

export const metadata: Metadata = {
  title: 'Fashion Forward Case Study - 520% Traffic Increase | RankRender',
  description: 'How Fashion Forward transformed their e-commerce SEO with RankRender, achieving 520% traffic increase and 410% revenue growth through product catalog optimization.',
}

export default function FashionForwardCaseStudy() {
  return (
    <>
      <Header />
      <main>
        {/* Hero Section */}
        <div className="bg-white py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-4xl">
              <div className="text-center">
                <div className="mb-6">
                  <span className="inline-flex items-center rounded-full bg-green-100 px-3 py-1 text-sm font-medium text-green-800">
                    E-commerce Success Story
                  </span>
                </div>
                <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl">
                  Fashion Forward: E-commerce SEO Transformation
                </h1>
                <p className="mt-6 text-xl leading-8 text-gray-600">
                  How a fashion retailer achieved 520% traffic increase and 410% revenue growth 
                  through comprehensive product catalog optimization and seasonal content strategy.
                </p>
              </div>
              
              {/* Key Metrics */}
              <div className="mt-16 grid grid-cols-2 gap-8 sm:grid-cols-4">
                <div className="text-center">
                  <div className="text-4xl font-bold text-rankrender-600">520%</div>
                  <div className="text-sm text-gray-600 mt-2">Traffic Increase</div>
                </div>
                <div className="text-center">
                  <div className="text-4xl font-bold text-rankrender-600">410%</div>
                  <div className="text-sm text-gray-600 mt-2">Revenue Growth</div>
                </div>
                <div className="text-center">
                  <div className="text-4xl font-bold text-rankrender-600">98%</div>
                  <div className="text-sm text-gray-600 mt-2">Product Coverage</div>
                </div>
                <div className="text-center">
                  <div className="text-4xl font-bold text-rankrender-600">10</div>
                  <div className="text-sm text-gray-600 mt-2">Months to Results</div>
                </div>
              </div>
            </div>
          </Container>
        </div>

        {/* Featured Image */}
        <div className="bg-gray-50 py-16">
          <Container>
            <div className="mx-auto max-w-4xl">
              <img
                src="https://images.unsplash.com/photo-1441986300917-64674bd600d8?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&h=600&q=80"
                alt="Fashion Forward E-commerce Platform"
                className="w-full rounded-lg shadow-lg"
              />
            </div>
          </Container>
        </div>

        {/* Company Overview */}
        <div className="bg-white py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-4xl">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
                <div className="lg:col-span-2">
                  <h2 className="text-3xl font-bold text-gray-900 mb-6">Company Overview</h2>
                  <div className="prose prose-lg prose-gray max-w-none">
                    <p>
                      Fashion Forward is a mid-sized online fashion retailer specializing in contemporary 
                      women's clothing, accessories, and seasonal collections. Founded in 2018, the company 
                      quickly grew to offer over 5,000 products across 50+ brands, with a focus on 
                      fast-fashion trends and seasonal collections.
                    </p>
                    <p>
                      Despite having a strong product catalog and competitive pricing, Fashion Forward 
                      struggled with organic visibility. Their JavaScript-heavy e-commerce platform, 
                      built on a modern React framework, was creating significant SEO challenges that 
                      prevented their products from being discovered by potential customers.
                    </p>
                  </div>
                </div>
                <div className="lg:col-span-1">
                  <div className="bg-gray-50 p-6 rounded-lg">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Company Details</h3>
                    <dl className="space-y-3 text-sm">
                      <div>
                        <dt className="font-medium text-gray-900">Industry</dt>
                        <dd className="text-gray-600">E-commerce Fashion</dd>
                      </div>
                      <div>
                        <dt className="font-medium text-gray-900">Founded</dt>
                        <dd className="text-gray-600">2018</dd>
                      </div>
                      <div>
                        <dt className="font-medium text-gray-900">Products</dt>
                        <dd className="text-gray-600">5,000+ SKUs</dd>
                      </div>
                      <div>
                        <dt className="font-medium text-gray-900">Platform</dt>
                        <dd className="text-gray-600">React/Next.js</dd>
                      </div>
                      <div>
                        <dt className="font-medium text-gray-900">Target Market</dt>
                        <dd className="text-gray-600">Women 18-35</dd>
                      </div>
                      <div>
                        <dt className="font-medium text-gray-900">Geographic Focus</dt>
                        <dd className="text-gray-600">US & Canada</dd>
                      </div>
                    </dl>
                  </div>
                </div>
              </div>
            </div>
          </Container>
        </div>

        {/* The Challenge */}
        <div className="bg-gray-50 py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-4xl">
              <h2 className="text-3xl font-bold text-gray-900 mb-8">The Challenge</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
                <div className="bg-white p-6 rounded-lg shadow-sm">
                  <div className="flex items-center mb-4">
                    <div className="h-8 w-8 bg-red-100 rounded-full flex items-center justify-center mr-3">
                      <span className="text-red-600 font-bold">!</span>
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900">Poor Product Discovery</h3>
                  </div>
                  <p className="text-gray-600">
                    Less than 15% of their product catalog was being indexed by search engines, 
                    making it nearly impossible for customers to discover their products through organic search.
                  </p>
                </div>
                
                <div className="bg-white p-6 rounded-lg shadow-sm">
                  <div className="flex items-center mb-4">
                    <div className="h-8 w-8 bg-red-100 rounded-full flex items-center justify-center mr-3">
                      <span className="text-red-600 font-bold">!</span>
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900">Seasonal Content Issues</h3>
                  </div>
                  <p className="text-gray-600">
                    New seasonal collections weren't being indexed quickly enough to capture 
                    peak shopping periods, resulting in missed revenue opportunities.
                  </p>
                </div>
                
                <div className="bg-white p-6 rounded-lg shadow-sm">
                  <div className="flex items-center mb-4">
                    <div className="h-8 w-8 bg-red-100 rounded-full flex items-center justify-center mr-3">
                      <span className="text-red-600 font-bold">!</span>
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900">JavaScript Rendering Problems</h3>
                  </div>
                  <p className="text-gray-600">
                    Their React-based platform wasn't properly rendering product information 
                    for search engine crawlers, leading to empty or incomplete product pages in search results.
                  </p>
                </div>
                
                <div className="bg-white p-6 rounded-lg shadow-sm">
                  <div className="flex items-center mb-4">
                    <div className="h-8 w-8 bg-red-100 rounded-full flex items-center justify-center mr-3">
                      <span className="text-red-600 font-bold">!</span>
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900">Mobile Performance</h3>
                  </div>
                  <p className="text-gray-600">
                    Slow mobile page load times were hurting both user experience and search rankings, 
                    particularly important for a mobile-first fashion audience.
                  </p>
                </div>
              </div>

              <div className="bg-white p-8 rounded-lg shadow-sm">
                <h3 className="text-xl font-semibold text-gray-900 mb-4">Initial Performance Metrics</h3>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-red-600">15%</div>
                    <div className="text-sm text-gray-600">Product Indexing Rate</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-red-600">4.2s</div>
                    <div className="text-sm text-gray-600">Mobile Load Time</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-red-600">12%</div>
                    <div className="text-sm text-gray-600">Organic Traffic Share</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-red-600">2.1%</div>
                    <div className="text-sm text-gray-600">Mobile Conversion Rate</div>
                  </div>
                </div>
              </div>
            </div>
          </Container>
        </div>

        {/* The Solution */}
        <div className="bg-white py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-4xl">
              <h2 className="text-3xl font-bold text-gray-900 mb-8">The RankRender Solution</h2>
              
              <div className="space-y-12">
                <div>
                  <h3 className="text-2xl font-semibold text-gray-900 mb-6">Phase 1: Technical Foundation (Months 1-2)</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div>
                      <h4 className="text-lg font-semibold text-gray-900 mb-3">Server-Side Rendering Implementation</h4>
                      <ul className="space-y-2 text-gray-600">
                        <li className="flex items-start">
                          <span className="text-rankrender-600 mr-2">•</span>
                          Implemented Next.js SSR for all product pages
                        </li>
                        <li className="flex items-start">
                          <span className="text-rankrender-600 mr-2">•</span>
                          Optimized critical rendering path for product catalogs
                        </li>
                        <li className="flex items-start">
                          <span className="text-rankrender-600 mr-2">•</span>
                          Added dynamic sitemap generation for new products
                        </li>
                      </ul>
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold text-gray-900 mb-3">Performance Optimization</h4>
                      <ul className="space-y-2 text-gray-600">
                        <li className="flex items-start">
                          <span className="text-rankrender-600 mr-2">•</span>
                          Implemented image lazy loading and optimization
                        </li>
                        <li className="flex items-start">
                          <span className="text-rankrender-600 mr-2">•</span>
                          Optimized JavaScript bundle splitting
                        </li>
                        <li className="flex items-start">
                          <span className="text-rankrender-600 mr-2">•</span>
                          Added CDN integration for static assets
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-2xl font-semibold text-gray-900 mb-6">Phase 2: Product Catalog Optimization (Months 3-5)</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div>
                      <h4 className="text-lg font-semibold text-gray-900 mb-3">Structured Data Implementation</h4>
                      <ul className="space-y-2 text-gray-600">
                        <li className="flex items-start">
                          <span className="text-rankrender-600 mr-2">•</span>
                          Product schema markup for all items
                        </li>
                        <li className="flex items-start">
                          <span className="text-rankrender-600 mr-2">•</span>
                          Review and rating schema integration
                        </li>
                        <li className="flex items-start">
                          <span className="text-rankrender-600 mr-2">•</span>
                          Offer and pricing schema for promotions
                        </li>
                      </ul>
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold text-gray-900 mb-3">Content Optimization</h4>
                      <ul className="space-y-2 text-gray-600">
                        <li className="flex items-start">
                          <span className="text-rankrender-600 mr-2">•</span>
                          Automated product description optimization
                        </li>
                        <li className="flex items-start">
                          <span className="text-rankrender-600 mr-2">•</span>
                          Category page content enhancement
                        </li>
                        <li className="flex items-start">
                          <span className="text-rankrender-600 mr-2">•</span>
                          Size guide and care instruction optimization
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-2xl font-semibold text-gray-900 mb-6">Phase 3: Seasonal Content Strategy (Months 6-8)</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div>
                      <h4 className="text-lg font-semibold text-gray-900 mb-3">Real-Time Indexing</h4>
                      <ul className="space-y-2 text-gray-600">
                        <li className="flex items-start">
                          <span className="text-rankrender-600 mr-2">•</span>
                          Automated sitemap updates for new collections
                        </li>
                        <li className="flex items-start">
                          <span className="text-rankrender-600 mr-2">•</span>
                          Priority indexing for seasonal products
                        </li>
                        <li className="flex items-start">
                          <span className="text-rankrender-600 mr-2">•</span>
                          Fast-track indexing for limited-time offers
                        </li>
                      </ul>
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold text-gray-900 mb-3">Trend-Based Optimization</h4>
                      <ul className="space-y-2 text-gray-600">
                        <li className="flex items-start">
                          <span className="text-rankrender-600 mr-2">•</span>
                          Seasonal keyword integration
                        </li>
                        <li className="flex items-start">
                          <span className="text-rankrender-600 mr-2">•</span>
                          Trend-based collection pages
                        </li>
                        <li className="flex items-start">
                          <span className="text-rankrender-600 mr-2">•</span>
                          Holiday and event-specific optimization
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-2xl font-semibold text-gray-900 mb-6">Phase 4: Mobile & Conversion Optimization (Months 9-10)</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div>
                      <h4 className="text-lg font-semibold text-gray-900 mb-3">Mobile Performance</h4>
                      <ul className="space-y-2 text-gray-600">
                        <li className="flex items-start">
                          <span className="text-rankrender-600 mr-2">•</span>
                          Progressive Web App implementation
                        </li>
                        <li className="flex items-start">
                          <span className="text-rankrender-600 mr-2">•</span>
                          Mobile-first indexing optimization
                        </li>
                        <li className="flex items-start">
                          <span className="text-rankrender-600 mr-2">•</span>
                          Touch-friendly interface improvements
                        </li>
                      </ul>
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold text-gray-900 mb-3">Conversion Enhancement</h4>
                      <ul className="space-y-2 text-gray-600">
                        <li className="flex items-start">
                          <span className="text-rankrender-600 mr-2">•</span>
                          Optimized product page layouts
                        </li>
                        <li className="flex items-start">
                          <span className="text-rankrender-600 mr-2">•</span>
                          Enhanced filtering and search functionality
                        </li>
                        <li className="flex items-start">
                          <span className="text-rankrender-600 mr-2">•</span>
                          Streamlined checkout process
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </Container>
        </div>

        {/* Results */}
        <div className="bg-gray-50 py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-4xl">
              <h2 className="text-3xl font-bold text-gray-900 mb-8">The Results</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
                <div className="bg-white p-8 rounded-lg shadow-sm">
                  <h3 className="text-xl font-semibold text-gray-900 mb-6">Traffic & Visibility</h3>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">Organic Traffic</span>
                      <span className="text-2xl font-bold text-green-600">+520%</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">Product Page Views</span>
                      <span className="text-2xl font-bold text-green-600">+680%</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">Product Indexing Rate</span>
                      <span className="text-2xl font-bold text-green-600">98%</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">Seasonal Collection Visibility</span>
                      <span className="text-2xl font-bold text-green-600">+750%</span>
                    </div>
                  </div>
                </div>
                
                <div className="bg-white p-8 rounded-lg shadow-sm">
                  <h3 className="text-xl font-semibold text-gray-900 mb-6">Revenue & Conversion</h3>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">Organic Revenue</span>
                      <span className="text-2xl font-bold text-green-600">+410%</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">Mobile Conversion Rate</span>
                      <span className="text-2xl font-bold text-green-600">+85%</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">Average Order Value</span>
                      <span className="text-2xl font-bold text-green-600">+32%</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">Return Customer Rate</span>
                      <span className="text-2xl font-bold text-green-600">+45%</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white p-8 rounded-lg shadow-sm">
                <h3 className="text-xl font-semibold text-gray-900 mb-6">Performance Improvements</h3>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-green-600">1.2s</div>
                    <div className="text-sm text-gray-600">Mobile Load Time</div>
                    <div className="text-xs text-gray-500">(from 4.2s)</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-green-600">95</div>
                    <div className="text-sm text-gray-600">Mobile PageSpeed Score</div>
                    <div className="text-xs text-gray-500">(from 45)</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-green-600">0.08</div>
                    <div className="text-sm text-gray-600">Cumulative Layout Shift</div>
                    <div className="text-xs text-gray-500">(from 0.35)</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-green-600">24h</div>
                    <div className="text-sm text-gray-600">New Product Indexing</div>
                    <div className="text-xs text-gray-500">(from 2-3 weeks)</div>
                  </div>
                </div>
              </div>
            </div>
          </Container>
        </div>

        {/* Testimonial */}
        <div className="bg-white py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-4xl">
              <div className="bg-gray-50 p-12 rounded-lg">
                <blockquote className="text-xl italic text-gray-700 mb-6">
                  "RankRender completely transformed our e-commerce SEO. Our seasonal collections now rank on the first page for competitive fashion keywords within days of launch. The product discovery improvements have been incredible - we're seeing customers find products we never thought would get organic traffic. The mobile performance improvements alone increased our conversion rate by 85%."
                </blockquote>
                <div className="flex items-center">
                  <div className="h-16 w-16 rounded-full bg-gray-200 flex items-center justify-center mr-4">
                    <span className="text-xl font-semibold text-gray-600">SC</span>
                  </div>
                  <div>
                    <div className="font-semibold text-gray-900">Sophie Chen</div>
                    <div className="text-gray-600">VP of Marketing, Fashion Forward</div>
                  </div>
                </div>
              </div>
            </div>
          </Container>
        </div>

        {/* Key Takeaways */}
        <div className="bg-gray-50 py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-4xl">
              <h2 className="text-3xl font-bold text-gray-900 mb-8">Key Takeaways</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div className="bg-white p-6 rounded-lg shadow-sm">
                  <div className="h-12 w-12 bg-rankrender-100 rounded-lg flex items-center justify-center mb-4">
                    <span className="text-2xl">🚀</span>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">Technical Foundation Matters</h3>
                  <p className="text-gray-600">
                    Proper JavaScript rendering and server-side optimization are crucial for e-commerce platforms 
                    to achieve product visibility in search results.
                  </p>
                </div>
                
                <div className="bg-white p-6 rounded-lg shadow-sm">
                  <div className="h-12 w-12 bg-rankrender-100 rounded-lg flex items-center justify-center mb-4">
                    <span className="text-2xl">⚡</span>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">Speed Drives Conversions</h3>
                  <p className="text-gray-600">
                    Mobile performance optimization directly impacts conversion rates, especially for fashion 
                    e-commerce where visual appeal and quick browsing are essential.
                  </p>
                </div>
                
                <div className="bg-white p-6 rounded-lg shadow-sm">
                  <div className="h-12 w-12 bg-rankrender-100 rounded-lg flex items-center justify-center mb-4">
                    <span className="text-2xl">📈</span>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">Seasonal Strategy Wins</h3>
                  <p className="text-gray-600">
                    Real-time indexing and trend-based optimization allow fashion retailers to capture 
                    seasonal demand and maximize revenue during peak periods.
                  </p>
                </div>
              </div>
            </div>
          </Container>
        </div>

        {/* CTA Section */}
        <div className="bg-rankrender-600 py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl text-center">
              <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl">
                Ready to transform your e-commerce SEO?
              </h2>
              <p className="mx-auto mt-6 max-w-xl text-lg leading-8 text-rankrender-100">
                See how RankRender can help your fashion or e-commerce platform achieve similar results 
                with our specialized optimization strategies.
              </p>
              <div className="mt-10 flex items-center justify-center gap-x-6">
                <Button href="/register" color="white">
                  Get Started Free
                </Button>
                <Button href="/contact" variant="outline" color="white">
                  Schedule Demo
                </Button>
              </div>
            </div>
          </Container>
        </div>
      </main>
      <Footer />
    </>
  )
}
