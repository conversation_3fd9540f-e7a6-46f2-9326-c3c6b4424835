import { type Metadata } from 'next'
import { Container } from '@/components/Container'
import { Header } from '@/components/Header'
import { Footer } from '@/components/Footer'
import { Button } from '@/components/Button'

export const metadata: Metadata = {
  title: 'Global Casino Network Case Study - 300% Traffic Increase | RankRender',
  description: 'How Global Casino Network achieved 300% traffic growth using RankRender Enterprise plan for their 5,000+ game portfolio.',
}

export default function GlobalCasinoNetworkCaseStudy() {
  return (
    <>
      <Header />
      <main>
        {/* Hero Section */}
        <div className="bg-white py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-4xl">
              <div className="text-center">
                <div className="mb-6">
                  <span className="inline-flex items-center rounded-full bg-purple-100 px-3 py-1 text-sm font-medium text-purple-800">
                    Enterprise Success Story
                  </span>
                </div>
                <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl">
                  Global Casino Network: Enterprise-Scale SEO Success
                </h1>
                <p className="mt-6 text-xl leading-8 text-gray-600">
                  How a major casino operator achieved 300% traffic growth with RankRender's 
                  Enterprise plan, optimizing 5,000+ game pages across multiple brands.
                </p>
              </div>
              
              {/* Key Metrics */}
              <div className="mt-16 grid grid-cols-2 gap-8 sm:grid-cols-4">
                <div className="text-center">
                  <div className="text-4xl font-bold text-rankrender-600">300%</div>
                  <div className="text-sm text-gray-600 mt-2">Traffic Increase</div>
                </div>
                <div className="text-center">
                  <div className="text-4xl font-bold text-rankrender-600">85%</div>
                  <div className="text-sm text-gray-600 mt-2">Faster Indexing</div>
                </div>
                <div className="text-center">
                  <div className="text-4xl font-bold text-rankrender-600">5,000+</div>
                  <div className="text-sm text-gray-600 mt-2">Games Optimized</div>
                </div>
                <div className="text-center">
                  <div className="text-4xl font-bold text-rankrender-600">3</div>
                  <div className="text-sm text-gray-600 mt-2">Months to Results</div>
                </div>
              </div>
            </div>
          </Container>
        </div>

        {/* Company Overview */}
        <div className="bg-gray-50 py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-4xl">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
                <div className="lg:col-span-2">
                  <h2 className="text-3xl font-bold text-gray-900 mb-6">The Challenge</h2>
                  <p className="text-gray-700 mb-6">
                    Global Casino Network operates multiple casino brands with over 5,000 slot games, 
                    table games, and live dealer experiences. Their JavaScript-heavy platform was 
                    struggling with search engine visibility, particularly for new game launches 
                    and promotional content.
                  </p>
                  <div className="bg-red-50 p-6 rounded-lg">
                    <h3 className="font-semibold text-red-900 mb-3">Critical Issues:</h3>
                    <ul className="space-y-2 text-red-800 text-sm">
                      <li>• Only 20% of game pages being indexed</li>
                      <li>• New games taking 3-4 weeks to appear in search</li>
                      <li>• Promotional content invisible to search engines</li>
                      <li>• Poor mobile performance affecting rankings</li>
                    </ul>
                  </div>
                </div>
                <div className="lg:col-span-1">
                  <div className="bg-white p-6 rounded-lg shadow-sm">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Company Profile</h3>
                    <dl className="space-y-3 text-sm">
                      <div>
                        <dt className="font-medium text-gray-900">Industry</dt>
                        <dd className="text-gray-600">Online Casino</dd>
                      </div>
                      <div>
                        <dt className="font-medium text-gray-900">Scale</dt>
                        <dd className="text-gray-600">5,000+ games</dd>
                      </div>
                      <div>
                        <dt className="font-medium text-gray-900">Brands</dt>
                        <dd className="text-gray-600">Multiple casino brands</dd>
                      </div>
                      <div>
                        <dt className="font-medium text-gray-900">Markets</dt>
                        <dd className="text-gray-600">Global operations</dd>
                      </div>
                    </dl>
                  </div>
                </div>
              </div>
            </div>
          </Container>
        </div>

        {/* RankRender Solution */}
        <div className="bg-white py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-4xl">
              <h2 className="text-3xl font-bold text-gray-900 mb-8">Why RankRender Enterprise?</h2>
              
              <div className="bg-purple-50 p-8 rounded-lg mb-12">
                <h3 className="text-xl font-semibold text-gray-900 mb-4">Enterprise Plan Selection</h3>
                <p className="text-gray-700 mb-6">
                  With 5,000+ game pages and multiple brands, Global Casino Network needed unlimited 
                  rendering capacity and enterprise-grade features. They chose RankRender's Enterprise 
                  plan for its scalability and multi-brand management capabilities.
                </p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-3">Enterprise Features Used:</h4>
                    <ul className="space-y-2 text-gray-700 text-sm">
                      <li>• Unlimited page renders</li>
                      <li>• Multi-brand dashboard</li>
                      <li>• Dedicated account manager</li>
                      <li>• 24/7 priority support</li>
                      <li>• Custom integrations</li>
                      <li>• SLA guarantees</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-3">Cost Justification:</h4>
                    <ul className="space-y-2 text-gray-700 text-sm">
                      <li>• 5,000+ pages = 500k+ renders/month</li>
                      <li>• Multiple brands requiring separate configs</li>
                      <li>• 24/7 operations needing instant support</li>
                      <li>• Custom compliance requirements</li>
                    </ul>
                  </div>
                </div>
              </div>

              <div className="space-y-8">
                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">Implementation Process</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="text-center">
                      <div className="h-12 w-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                        <span className="text-xl">1️⃣</span>
                      </div>
                      <h4 className="font-semibold text-gray-900 mb-2">Account Setup</h4>
                      <p className="text-sm text-gray-600">Enterprise account with dedicated manager assigned</p>
                    </div>
                    <div className="text-center">
                      <div className="h-12 w-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                        <span className="text-xl">2️⃣</span>
                      </div>
                      <h4 className="font-semibold text-gray-900 mb-2">Multi-Brand Config</h4>
                      <p className="text-sm text-gray-600">All casino brands added to unified dashboard</p>
                    </div>
                    <div className="text-center">
                      <div className="h-12 w-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                        <span className="text-xl">3️⃣</span>
                      </div>
                      <h4 className="font-semibold text-gray-900 mb-2">Full Rollout</h4>
                      <p className="text-sm text-gray-600">5,000+ game pages optimized simultaneously</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </Container>
        </div>

        {/* Results */}
        <div className="bg-gray-50 py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-4xl">
              <h2 className="text-3xl font-bold text-gray-900 mb-8">Enterprise-Scale Results</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
                <div className="bg-white p-8 rounded-lg shadow-sm">
                  <h3 className="text-xl font-semibold text-gray-900 mb-6">Traffic & Visibility</h3>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">Organic Traffic</span>
                      <span className="text-2xl font-bold text-green-600">+300%</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">Game Page Indexing</span>
                      <span className="text-2xl font-bold text-green-600">95%</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">Promotion Visibility</span>
                      <span className="text-2xl font-bold text-green-600">+200%</span>
                    </div>
                  </div>
                </div>
                
                <div className="bg-white p-8 rounded-lg shadow-sm">
                  <h3 className="text-xl font-semibold text-gray-900 mb-6">Performance & Speed</h3>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">PageSpeed Score</span>
                      <span className="text-2xl font-bold text-green-600">+60%</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">Indexing Speed</span>
                      <span className="text-2xl font-bold text-green-600">85% Faster</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">New Game Visibility</span>
                      <span className="text-2xl font-bold text-green-600">24 Hours</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white p-8 rounded-lg shadow-sm">
                <h3 className="text-xl font-semibold text-gray-900 mb-6">Enterprise ROI Analysis</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-purple-600">$50K+</div>
                    <div className="text-sm text-gray-600">Monthly Enterprise Plan</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-green-600">$2M+</div>
                    <div className="text-sm text-gray-600">Additional Monthly Revenue</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-green-600">40:1</div>
                    <div className="text-sm text-gray-600">ROI Ratio</div>
                  </div>
                </div>
              </div>
            </div>
          </Container>
        </div>

        {/* Testimonial */}
        <div className="bg-white py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-4xl">
              <div className="bg-gray-50 p-12 rounded-lg">
                <blockquote className="text-xl italic text-gray-700 mb-6">
                  "RankRender's Enterprise plan was the only solution that could handle our scale. With 5,000+ games across multiple brands, we needed unlimited rendering and the multi-brand dashboard has been incredible. Our dedicated account manager helped us optimize each brand's specific needs. The ROI has been phenomenal - we're seeing 40x return on our investment."
                </blockquote>
                <div className="flex items-center">
                  <div className="h-16 w-16 rounded-full bg-gray-200 flex items-center justify-center mr-4">
                    <span className="text-xl font-semibold text-gray-600">MR</span>
                  </div>
                  <div>
                    <div className="font-semibold text-gray-900">Marcus Rodriguez</div>
                    <div className="text-gray-600">Head of Digital Marketing, Global Casino Network</div>
                  </div>
                </div>
              </div>
            </div>
          </Container>
        </div>

        {/* CTA Section */}
        <div className="bg-rankrender-600 py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl text-center">
              <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl">
                Ready for Enterprise-Scale SEO?
              </h2>
              <p className="mx-auto mt-6 max-w-xl text-lg leading-8 text-rankrender-100">
                See how RankRender's Enterprise plan can transform your large-scale 
                casino or gaming operation with unlimited rendering and dedicated support.
              </p>
              <div className="mt-10 flex items-center justify-center gap-x-6">
                <Button href="/contact" color="white">
                  Contact Sales
                </Button>
                <Button href="/register" variant="outline" color="white">
                  Start Free
                </Button>
              </div>
            </div>
          </Container>
        </div>
      </main>
      <Footer />
    </>
  )
}
