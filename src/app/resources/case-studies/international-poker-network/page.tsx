import { type Metadata } from 'next'
import { Container } from '@/components/Container'
import { Header } from '@/components/Header'
import { Footer } from '@/components/Footer'
import { Button } from '@/components/Button'

export const metadata: Metadata = {
  title: 'International Poker Network Case Study - 400% Growth | RankRender',
  description: 'How International Poker Network achieved 400% tournament visibility growth using RankRender Starter plan.',
}

export default function InternationalPokerNetworkCaseStudy() {
  return (
    <>
      <Header />
      <main>
        {/* Hero Section */}
        <div className="bg-white py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-4xl">
              <div className="text-center">
                <div className="mb-6">
                  <span className="inline-flex items-center rounded-full bg-green-100 px-3 py-1 text-sm font-medium text-green-800">
                    Starter Plan Success
                  </span>
                </div>
                <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl">
                  International Poker Network: Tournament SEO Success
                </h1>
                <p className="mt-6 text-xl leading-8 text-gray-600">
                  How a growing poker platform achieved 400% tournament visibility growth 
                  with RankRender's cost-effective Starter plan in just 6 weeks.
                </p>
              </div>
              
              {/* Key Metrics */}
              <div className="mt-16 grid grid-cols-2 gap-8 sm:grid-cols-4">
                <div className="text-center">
                  <div className="text-4xl font-bold text-rankrender-600">400%</div>
                  <div className="text-sm text-gray-600 mt-2">Tournament Visibility</div>
                </div>
                <div className="text-center">
                  <div className="text-4xl font-bold text-rankrender-600">75%</div>
                  <div className="text-sm text-gray-600 mt-2">More Registrations</div>
                </div>
                <div className="text-center">
                  <div className="text-4xl font-bold text-rankrender-600">$99</div>
                  <div className="text-sm text-gray-600 mt-2">Monthly Investment</div>
                </div>
                <div className="text-center">
                  <div className="text-4xl font-bold text-rankrender-600">6</div>
                  <div className="text-sm text-gray-600 mt-2">Weeks to Results</div>
                </div>
              </div>
            </div>
          </Container>
        </div>

        {/* Challenge & Solution */}
        <div className="bg-gray-50 py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-4xl">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
                <div>
                  <h2 className="text-3xl font-bold text-gray-900 mb-6">The Challenge</h2>
                  <p className="text-gray-700 mb-6">
                    International Poker Network was a growing platform struggling with tournament 
                    and leaderboard visibility. Their JavaScript-heavy tournament pages weren't 
                    being indexed, causing them to miss potential players searching for poker events.
                  </p>
                  <div className="bg-red-50 p-6 rounded-lg mb-6">
                    <h3 className="font-semibold text-red-900 mb-3">Key Issues:</h3>
                    <ul className="space-y-2 text-red-800 text-sm">
                      <li>• Tournament pages invisible to search engines</li>
                      <li>• Leaderboards not being indexed</li>
                      <li>• Low organic player acquisition</li>
                      <li>• Poor social media sharing</li>
                    </ul>
                  </div>
                  <div className="bg-white p-6 rounded-lg shadow-sm">
                    <h3 className="font-semibold text-gray-900 mb-3">Company Profile:</h3>
                    <ul className="space-y-2 text-gray-700 text-sm">
                      <li>• Growing poker platform</li>
                      <li>• ~500 tournament pages</li>
                      <li>• Limited marketing budget</li>
                      <li>• Focus on organic growth</li>
                    </ul>
                  </div>
                </div>
                <div>
                  <h2 className="text-3xl font-bold text-gray-900 mb-6">Why Starter Plan?</h2>
                  <div className="bg-green-50 p-6 rounded-lg mb-6">
                    <h3 className="font-semibold text-green-900 mb-3">Perfect for Growing Platforms:</h3>
                    <ul className="space-y-2 text-green-800 text-sm">
                      <li>• 10,000 renders/month (enough for 500 tournaments)</li>
                      <li>• Advanced SEO optimization</li>
                      <li>• Email support for quick help</li>
                      <li>• Basic analytics dashboard</li>
                      <li>• iGaming compliance checks</li>
                      <li>• Affordable $99/month pricing</li>
                    </ul>
                  </div>
                  <div className="bg-white p-6 rounded-lg shadow-sm">
                    <h3 className="font-semibold text-gray-900 mb-3">Budget Comparison:</h3>
                    <div className="space-y-3 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Previous SEO Agency:</span>
                        <span className="text-red-600">$2,500/month</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">RankRender Starter:</span>
                        <span className="text-green-600">$99/month</span>
                      </div>
                      <div className="flex justify-between font-semibold">
                        <span className="text-gray-900">Monthly Savings:</span>
                        <span className="text-green-600">$2,401</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </Container>
        </div>

        {/* Implementation */}
        <div className="bg-white py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-4xl">
              <h2 className="text-3xl font-bold text-gray-900 mb-8">Starter Plan Implementation</h2>
              
              <div className="space-y-8">
                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">Quick Setup Process</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="text-center">
                      <div className="h-12 w-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                        <span className="text-xl">1️⃣</span>
                      </div>
                      <h4 className="font-semibold text-gray-900 mb-2">Account Creation</h4>
                      <p className="text-sm text-gray-600">Signed up for Starter plan in 5 minutes</p>
                    </div>
                    <div className="text-center">
                      <div className="h-12 w-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                        <span className="text-xl">2️⃣</span>
                      </div>
                      <h4 className="font-semibold text-gray-900 mb-2">Domain Setup</h4>
                      <p className="text-sm text-gray-600">Added pokernetwork.com to dashboard</p>
                    </div>
                    <div className="text-center">
                      <div className="h-12 w-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                        <span className="text-xl">3️⃣</span>
                      </div>
                      <h4 className="font-semibold text-gray-900 mb-2">Tournament Focus</h4>
                      <p className="text-sm text-gray-600">Configured tournament and leaderboard URLs</p>
                    </div>
                  </div>
                </div>

                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">Starter Plan Features Used</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-3">Optimization Rules:</h4>
                      <ul className="space-y-2 text-gray-700 text-sm">
                        <li>• Tournament pages: /tournaments/*</li>
                        <li>• Leaderboards: /leaderboards/*</li>
                        <li>• Player profiles: /players/*</li>
                        <li>• Live events: /live/*</li>
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-3">Monthly Usage:</h4>
                      <ul className="space-y-2 text-gray-700 text-sm">
                        <li>• ~8,500 renders used (85% of limit)</li>
                        <li>• 500+ tournament pages optimized</li>
                        <li>• 200+ leaderboard pages indexed</li>
                        <li>• 24-hour cache freshness</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </Container>
        </div>

        {/* Results */}
        <div className="bg-gray-50 py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-4xl">
              <h2 className="text-3xl font-bold text-gray-900 mb-8">Starter Plan Results</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
                <div className="bg-white p-8 rounded-lg shadow-sm">
                  <h3 className="text-xl font-semibold text-gray-900 mb-6">Tournament Performance</h3>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">Tournament Visibility</span>
                      <span className="text-2xl font-bold text-green-600">+400%</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">Player Registrations</span>
                      <span className="text-2xl font-bold text-green-600">+75%</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">Social Sharing</span>
                      <span className="text-2xl font-bold text-green-600">+180%</span>
                    </div>
                  </div>
                </div>
                
                <div className="bg-white p-8 rounded-lg shadow-sm">
                  <h3 className="text-xl font-semibold text-gray-900 mb-6">Technical Improvements</h3>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">Pages Indexed</span>
                      <span className="text-2xl font-bold text-green-600">95%</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">Leaderboard Indexing</span>
                      <span className="text-2xl font-bold text-green-600">Real-time</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">Implementation Time</span>
                      <span className="text-2xl font-bold text-blue-600">1 Day</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white p-8 rounded-lg shadow-sm">
                <h3 className="text-xl font-semibold text-gray-900 mb-6">Starter Plan ROI</h3>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-green-600">$99</div>
                    <div className="text-sm text-gray-600">Monthly Investment</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-green-600">$12K</div>
                    <div className="text-sm text-gray-600">Additional Monthly Revenue</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-green-600">121:1</div>
                    <div className="text-sm text-gray-600">ROI Ratio</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-green-600">$2,401</div>
                    <div className="text-sm text-gray-600">Monthly Savings vs Agency</div>
                  </div>
                </div>
              </div>
            </div>
          </Container>
        </div>

        {/* Testimonial */}
        <div className="bg-white py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-4xl">
              <div className="bg-gray-50 p-12 rounded-lg">
                <blockquote className="text-xl italic text-gray-700 mb-6">
                  "As a growing poker platform, budget was crucial. The Starter plan at $99/month was perfect - we were paying $2,500/month to an SEO agency with poor results. RankRender's Starter plan gave us 400% better tournament visibility in just 6 weeks. The 10,000 renders per month handles all our tournaments perfectly, and we're saving over $2,400 monthly while getting much better results."
                </blockquote>
                <div className="flex items-center">
                  <div className="h-16 w-16 rounded-full bg-gray-200 flex items-center justify-center mr-4">
                    <span className="text-xl font-semibold text-gray-600">AL</span>
                  </div>
                  <div>
                    <div className="font-semibold text-gray-900">Alex Liu</div>
                    <div className="text-gray-600">Marketing Manager, International Poker Network</div>
                  </div>
                </div>
              </div>
            </div>
          </Container>
        </div>

        {/* CTA Section */}
        <div className="bg-rankrender-600 py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl text-center">
              <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl">
                Ready to Grow Your Gaming Platform?
              </h2>
              <p className="mx-auto mt-6 max-w-xl text-lg leading-8 text-rankrender-100">
                See how RankRender's affordable Starter plan can boost your tournament 
                visibility and player acquisition without breaking the budget.
              </p>
              <div className="mt-10 flex items-center justify-center gap-x-6">
                <Button href="/register" color="white">
                  Start for $99/month
                </Button>
                <Button href="/register" variant="outline" color="white">
                  Try Free First
                </Button>
              </div>
            </div>
          </Container>
        </div>
      </main>
      <Footer />
    </>
  )
}
