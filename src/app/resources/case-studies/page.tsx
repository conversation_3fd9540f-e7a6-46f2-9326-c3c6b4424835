import { type Metadata } from 'next'
import { Container } from '@/components/Container'
import { Header } from '@/components/Header'
import { Footer } from '@/components/Footer'
import { Button } from '@/components/Button'
import Link from 'next/link'

export const metadata: Metadata = {
  title: 'Case Studies',
  description: 'Success stories from leading iGaming operators who use RankRender to boost their SEO performance.',
}

const caseStudies = [
  {
    id: 1,
    title: 'Major Casino Operator Increases Organic Traffic by 300%',
    company: 'Global Casino Network',
    industry: 'Online Casino',
    challenge: 'Poor indexing of slot games and promotional content',
    solution: 'Implemented RankRender\'s real-time optimization for 5,000+ game pages',
    results: [
      '300% increase in organic traffic',
      '85% faster game indexing',
      '200% more promotion visibility',
      '60% improvement in PageSpeed scores'
    ],
    timeframe: '3 months',
    href: '/resources/case-studies/global-casino-network',
    image: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2340&q=80',
  },
  {
    id: 2,
    title: 'Sports Betting Platform Dominates Live Odds SEO',
    company: 'Premier Sportsbook',
    industry: 'Sports Betting',
    challenge: 'Dynamic odds pages not being indexed by search engines',
    solution: 'Custom optimization for live betting interfaces and odds updates',
    results: [
      '250% increase in sports betting traffic',
      '90% faster odds page indexing',
      'Top 3 rankings for major sporting events',
      '150% improvement in mobile search visibility'
    ],
    timeframe: '2 months',
    href: '/resources/case-studies/premier-sportsbook',
    image: 'https://images.unsplash.com/photo-1461896836934-ffe607ba8211?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2340&q=80',
  },
  {
    id: 3,
    title: 'Poker Network Achieves 400% Growth in Tournament Visibility',
    company: 'International Poker Network',
    industry: 'Poker Platform',
    challenge: 'Tournament pages and leaderboards invisible to search engines',
    solution: 'Specialized optimization for time-sensitive poker content',
    results: [
      '400% increase in tournament page visibility',
      '75% more player registrations from organic search',
      'Real-time leaderboard indexing',
      '180% improvement in social media sharing'
    ],
    timeframe: '6 weeks',
    href: '/resources/case-studies/international-poker-network',
    image: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2340&q=80',
  },
  {
    id: 4,
    title: 'Multi-Brand Operator Streamlines SEO Across 12 Platforms',
    company: 'European Gaming Group',
    industry: 'Multi-Platform Operator',
    challenge: 'Managing SEO for multiple casino and betting brands',
    solution: 'Centralized RankRender dashboard for multi-brand optimization',
    results: [
      '220% average increase across all brands',
      '50% reduction in SEO management time',
      'Consistent optimization across 12 platforms',
      '95% improvement in brand cross-promotion'
    ],
    timeframe: '4 months',
    href: '/resources/case-studies/european-gaming-group',
    image: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2426&q=80',
  },
  {
    id: 5,
    title: 'Fashion Retailer Achieves 520% Traffic Growth with Product Optimization',
    company: 'Fashion Forward',
    industry: 'E-commerce',
    challenge: 'Poor product discovery and seasonal content indexing issues',
    solution: 'Comprehensive product catalog optimization and real-time seasonal content strategy',
    results: [
      '520% increase in organic traffic',
      '410% revenue growth from organic search',
      '98% product indexing coverage',
      '85% improvement in mobile conversion rate'
    ],
    timeframe: '10 months',
    href: '/resources/case-studies/fashion-forward',
    image: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&h=600&q=80',
  },
]

const testimonials = [
  {
    body: 'RankRender transformed our SEO performance. Our slot games now get indexed within hours instead of weeks, and our organic traffic has tripled.',
    author: {
      name: 'Maria Santos',
      handle: 'mariasantos',
      title: 'Head of Digital Marketing',
      company: 'Global Casino Network',
      imageUrl: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80',
    },
  },
  {
    body: 'The real-time optimization for our sports betting odds is incredible. We\'re now ranking for live events as they happen.',
    author: {
      name: 'James Wilson',
      handle: 'jameswilson',
      title: 'SEO Director',
      company: 'Premier Sportsbook',
      imageUrl: 'https://images.unsplash.com/photo-1519244703995-f4e0f30006d5?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80',
    },
  },
  {
    body: 'Managing SEO for 12 different gaming brands was a nightmare. RankRender\'s centralized approach has been a game-changer for our operations.',
    author: {
      name: 'Sophie Chen',
      handle: 'sophiechen',
      title: 'VP of Marketing',
      company: 'European Gaming Group',
      imageUrl: 'https://images.unsplash.com/photo-1517841905240-472988babdf9?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80',
    },
  },
]

export default function CaseStudies() {
  return (
    <>
      <Header />
      <main>
        {/* Hero Section */}
        <div className="bg-white py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl text-center">
              <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl">
                Success Stories
              </h1>
              <p className="mt-6 text-lg leading-8 text-gray-600">
                See how leading iGaming operators use RankRender to boost their SEO performance,
                increase organic traffic, and attract more players.
              </p>
            </div>
          </Container>
        </div>

        {/* Case Studies Grid */}
        <div className="bg-gray-50 py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl lg:text-center mb-16">
              <h2 className="text-base font-semibold leading-7 text-rankrender-600">Proven results</h2>
              <p className="mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                Real success from real iGaming operators
              </p>
            </div>
            <div className="mx-auto grid max-w-2xl grid-cols-1 gap-x-8 gap-y-16 lg:mx-0 lg:max-w-none lg:grid-cols-2">
              {caseStudies.map((study) => (
                <article key={study.id} className="flex flex-col items-start bg-white rounded-2xl p-8 shadow-sm ring-1 ring-gray-200">
                  <div className="relative w-full">
                    <img
                      className="aspect-[16/9] w-full rounded-2xl bg-gray-100 object-cover sm:aspect-[2/1] lg:aspect-[3/2]"
                      src={study.image}
                      alt=""
                    />
                    <div className="absolute inset-0 rounded-2xl ring-1 ring-inset ring-gray-900/10" />
                  </div>
                  <div className="max-w-xl">
                    <div className="mt-8 flex items-center gap-x-4 text-xs">
                      <span className="text-gray-500">{study.timeframe} implementation</span>
                      <span className="relative z-10 rounded-full bg-gray-50 px-3 py-1.5 font-medium text-gray-600">
                        {study.industry}
                      </span>
                    </div>
                    <div className="group relative">
                      <h3 className="mt-3 text-lg font-semibold leading-6 text-gray-900 group-hover:text-gray-600">
                        <Link href={study.href}>
                          <span className="absolute inset-0" />
                          {study.title}
                        </Link>
                      </h3>
                      <p className="mt-2 text-sm text-gray-600 font-medium">{study.company}</p>
                      <p className="mt-3 text-sm leading-6 text-gray-600">
                        <strong>Challenge:</strong> {study.challenge}
                      </p>
                      <p className="mt-2 text-sm leading-6 text-gray-600">
                        <strong>Solution:</strong> {study.solution}
                      </p>
                    </div>
                    <div className="mt-4">
                      <h4 className="text-sm font-semibold text-gray-900">Key Results:</h4>
                      <ul className="mt-2 space-y-1">
                        {study.results.map((result, index) => (
                          <li key={index} className="flex items-center text-sm text-gray-600">
                            <svg className="mr-2 h-4 w-4 text-rankrender-600" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clipRule="evenodd" />
                            </svg>
                            {result}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </article>
              ))}
            </div>
          </Container>
        </div>

        {/* Testimonials */}
        <div className="bg-white py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl lg:text-center">
              <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                What our clients say
              </h2>
              <p className="mt-6 text-lg leading-8 text-gray-600">
                Hear directly from iGaming operators who have transformed their SEO with RankRender.
              </p>
            </div>
            <div className="mx-auto mt-16 flow-root max-w-2xl sm:mt-20 lg:mx-0 lg:max-w-none">
              <div className="-mt-8 sm:-mx-4 sm:columns-1 sm:text-[0] lg:columns-3">
                {testimonials.map((testimonial) => (
                  <div key={testimonial.author.handle} className="pt-8 sm:inline-block sm:w-full sm:px-4">
                    <figure className="rounded-2xl bg-gray-50 p-8 text-sm leading-6">
                      <blockquote className="text-gray-900">
                        <p>"{testimonial.body}"</p>
                      </blockquote>
                      <figcaption className="mt-6 flex items-center gap-x-4">
                        <img
                          className="h-10 w-10 rounded-full bg-gray-50"
                          src={testimonial.author.imageUrl}
                          alt=""
                        />
                        <div>
                          <div className="font-semibold text-gray-900">{testimonial.author.name}</div>
                          <div className="text-gray-600">{testimonial.author.title}, {testimonial.author.company}</div>
                        </div>
                      </figcaption>
                    </figure>
                  </div>
                ))}
              </div>
            </div>
          </Container>
        </div>

        {/* Stats Section */}
        <div className="bg-gray-50 py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl lg:text-center">
              <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                Results that speak for themselves
              </h2>
            </div>
            <div className="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-x-8 gap-y-16 sm:mt-20 lg:mx-0 lg:max-w-none lg:grid-cols-4">
              <div className="flex flex-col items-center text-center">
                <div className="text-4xl font-bold tracking-tight text-rankrender-600">
                  250%
                </div>
                <div className="text-sm leading-6 text-gray-600 mt-2">
                  Average traffic increase
                </div>
                <p className="mt-4 text-sm text-gray-500">
                  Across all iGaming clients within 3 months
                </p>
              </div>
              <div className="flex flex-col items-center text-center">
                <div className="text-4xl font-bold tracking-tight text-rankrender-600">
                  85%
                </div>
                <div className="text-sm leading-6 text-gray-600 mt-2">
                  Faster content indexing
                </div>
                <p className="mt-4 text-sm text-gray-500">
                  Games and promotions indexed in hours, not days
                </p>
              </div>
              <div className="flex flex-col items-center text-center">
                <div className="text-4xl font-bold tracking-tight text-rankrender-600">
                  50+
                </div>
                <div className="text-sm leading-6 text-gray-600 mt-2">
                  iGaming operators
                </div>
                <p className="mt-4 text-sm text-gray-500">
                  Trust RankRender for their SEO optimization
                </p>
              </div>
              <div className="flex flex-col items-center text-center">
                <div className="text-4xl font-bold tracking-tight text-rankrender-600">
                  99.9%
                </div>
                <div className="text-sm leading-6 text-gray-600 mt-2">
                  Uptime guarantee
                </div>
                <p className="mt-4 text-sm text-gray-500">
                  Reliable optimization for 24/7 gaming operations
                </p>
              </div>
            </div>
          </Container>
        </div>

        {/* CTA Section */}
        <div className="bg-rankrender-600 py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl text-center">
              <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl">
                Ready to join these success stories?
              </h2>
              <p className="mx-auto mt-6 max-w-xl text-lg leading-8 text-rankrender-100">
                See how RankRender can transform your iGaming platform's SEO performance and drive more players to your games.
              </p>
              <div className="mt-10 flex items-center justify-center gap-x-6">
                <Button href="/register" color="white">
                  Start Your Success Story
                </Button>
                <Button href="/contact" variant="outline" color="white">
                  Get Custom Analysis
                </Button>
              </div>
            </div>
          </Container>
        </div>
      </main>
      <Footer />
    </>
  )
}
