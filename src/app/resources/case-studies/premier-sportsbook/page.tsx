import { type Metadata } from 'next'
import { Container } from '@/components/Container'
import { Header } from '@/components/Header'
import { Footer } from '@/components/Footer'
import { Button } from '@/components/Button'

export const metadata: Metadata = {
  title: 'Premier Sportsbook Case Study - 250% Traffic Growth | RankRender',
  description: 'How Premier Sportsbook achieved 250% traffic growth using RankRender Professional plan for live odds optimization.',
}

export default function PremierSportsbookCaseStudy() {
  return (
    <>
      <Header />
      <main>
        {/* Hero Section */}
        <div className="bg-white py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-4xl">
              <div className="text-center">
                <div className="mb-6">
                  <span className="inline-flex items-center rounded-full bg-blue-100 px-3 py-1 text-sm font-medium text-blue-800">
                    Professional Plan Success
                  </span>
                </div>
                <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl">
                  Premier Sportsbook: Live Odds SEO Mastery
                </h1>
                <p className="mt-6 text-xl leading-8 text-gray-600">
                  How a growing sportsbook achieved 250% traffic growth with RankRender's 
                  Professional plan, optimizing dynamic odds and live betting content.
                </p>
              </div>
              
              {/* Key Metrics */}
              <div className="mt-16 grid grid-cols-2 gap-8 sm:grid-cols-4">
                <div className="text-center">
                  <div className="text-4xl font-bold text-rankrender-600">250%</div>
                  <div className="text-sm text-gray-600 mt-2">Traffic Increase</div>
                </div>
                <div className="text-center">
                  <div className="text-4xl font-bold text-rankrender-600">90%</div>
                  <div className="text-sm text-gray-600 mt-2">Faster Indexing</div>
                </div>
                <div className="text-center">
                  <div className="text-4xl font-bold text-rankrender-600">Top 3</div>
                  <div className="text-sm text-gray-600 mt-2">Event Rankings</div>
                </div>
                <div className="text-center">
                  <div className="text-4xl font-bold text-rankrender-600">2</div>
                  <div className="text-sm text-gray-600 mt-2">Months to Results</div>
                </div>
              </div>
            </div>
          </Container>
        </div>

        {/* Challenge & Solution */}
        <div className="bg-gray-50 py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-4xl">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
                <div>
                  <h2 className="text-3xl font-bold text-gray-900 mb-6">The Challenge</h2>
                  <p className="text-gray-700 mb-6">
                    Premier Sportsbook's dynamic odds pages were invisible to search engines. 
                    Live betting content, event pages, and real-time odds updates weren't 
                    being indexed, causing them to miss crucial traffic during major sporting events.
                  </p>
                  <div className="bg-red-50 p-6 rounded-lg">
                    <h3 className="font-semibold text-red-900 mb-3">Key Issues:</h3>
                    <ul className="space-y-2 text-red-800 text-sm">
                      <li>• Dynamic odds pages not indexed</li>
                      <li>• Missing traffic during live events</li>
                      <li>• Poor mobile search visibility</li>
                      <li>• Slow content discovery by search engines</li>
                    </ul>
                  </div>
                </div>
                <div>
                  <h2 className="text-3xl font-bold text-gray-900 mb-6">Why Professional Plan?</h2>
                  <div className="bg-blue-50 p-6 rounded-lg mb-6">
                    <h3 className="font-semibold text-blue-900 mb-3">Perfect Fit for Growing Sportsbook:</h3>
                    <ul className="space-y-2 text-blue-800 text-sm">
                      <li>• 100,000 renders/month capacity</li>
                      <li>• AI search engine visibility</li>
                      <li>• Priority support for live events</li>
                      <li>• Advanced analytics for performance tracking</li>
                      <li>• API access for custom integrations</li>
                    </ul>
                  </div>
                  <div className="bg-white p-6 rounded-lg shadow-sm">
                    <h3 className="font-semibold text-gray-900 mb-3">Cost Analysis:</h3>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Monthly Plan Cost:</span>
                        <span className="font-medium">$299</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Pages Optimized:</span>
                        <span className="font-medium">~50,000/month</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Cost per Page:</span>
                        <span className="font-medium text-green-600">$0.006</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </Container>
        </div>

        {/* Implementation */}
        <div className="bg-white py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-4xl">
              <h2 className="text-3xl font-bold text-gray-900 mb-8">Professional Plan Implementation</h2>
              
              <div className="space-y-8">
                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">Phase 1: Account Setup & Configuration</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-3">Domain Configuration:</h4>
                      <ul className="space-y-2 text-gray-700 text-sm">
                        <li>• Added premiersportsbook.com to dashboard</li>
                        <li>• Configured live odds URL patterns</li>
                        <li>• Set up event-specific optimization rules</li>
                        <li>• Enabled mobile-first indexing</li>
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-3">Professional Features Activated:</h4>
                      <ul className="space-y-2 text-gray-700 text-sm">
                        <li>• Premium SEO optimization</li>
                        <li>• AI search engine visibility</li>
                        <li>• Priority support queue</li>
                        <li>• Advanced analytics dashboard</li>
                      </ul>
                    </div>
                  </div>
                </div>

                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">Phase 2: Live Odds Optimization</h3>
                  <p className="text-gray-700 mb-4">
                    Used RankRender's API access to automatically submit new odds pages and live betting 
                    content for immediate indexing during major sporting events.
                  </p>
                  <div className="bg-green-50 p-4 rounded-lg">
                    <p className="text-sm text-green-800">
                      <strong>Result:</strong> Live odds pages now indexed within 15 minutes of creation, 
                      capturing traffic during peak betting periods.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </Container>
        </div>

        {/* Results */}
        <div className="bg-gray-50 py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-4xl">
              <h2 className="text-3xl font-bold text-gray-900 mb-8">Professional Plan Results</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
                <div className="bg-white p-8 rounded-lg shadow-sm">
                  <h3 className="text-xl font-semibold text-gray-900 mb-6">Traffic & Rankings</h3>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">Sports Betting Traffic</span>
                      <span className="text-2xl font-bold text-green-600">+250%</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">Major Event Rankings</span>
                      <span className="text-2xl font-bold text-green-600">Top 3</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">Mobile Visibility</span>
                      <span className="text-2xl font-bold text-green-600">+150%</span>
                    </div>
                  </div>
                </div>
                
                <div className="bg-white p-8 rounded-lg shadow-sm">
                  <h3 className="text-xl font-semibold text-gray-900 mb-6">Performance Metrics</h3>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">Odds Page Indexing</span>
                      <span className="text-2xl font-bold text-green-600">90% Faster</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">Live Content Discovery</span>
                      <span className="text-2xl font-bold text-green-600">15 Minutes</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">Monthly Renders Used</span>
                      <span className="text-2xl font-bold text-blue-600">75,000</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white p-8 rounded-lg shadow-sm">
                <h3 className="text-xl font-semibold text-gray-900 mb-6">Professional Plan ROI</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-blue-600">$299</div>
                    <div className="text-sm text-gray-600">Monthly Investment</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-green-600">$45K</div>
                    <div className="text-sm text-gray-600">Additional Monthly Revenue</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-green-600">150:1</div>
                    <div className="text-sm text-gray-600">ROI Ratio</div>
                  </div>
                </div>
              </div>
            </div>
          </Container>
        </div>

        {/* Testimonial */}
        <div className="bg-white py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-4xl">
              <div className="bg-gray-50 p-12 rounded-lg">
                <blockquote className="text-xl italic text-gray-700 mb-6">
                  "The Professional plan was perfect for our growing sportsbook. We needed more than the Starter plan could offer, but weren't ready for Enterprise. The 100k renders per month handles all our live odds perfectly, and the priority support has been crucial during major sporting events. The ROI speaks for itself - 150x return on our $299 monthly investment."
                </blockquote>
                <div className="flex items-center">
                  <div className="h-16 w-16 rounded-full bg-gray-200 flex items-center justify-center mr-4">
                    <span className="text-xl font-semibold text-gray-600">JT</span>
                  </div>
                  <div>
                    <div className="font-semibold text-gray-900">Jake Thompson</div>
                    <div className="text-gray-600">Digital Marketing Director, Premier Sportsbook</div>
                  </div>
                </div>
              </div>
            </div>
          </Container>
        </div>

        {/* CTA Section */}
        <div className="bg-rankrender-600 py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl text-center">
              <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl">
                Ready to Dominate Sports Betting SEO?
              </h2>
              <p className="mx-auto mt-6 max-w-xl text-lg leading-8 text-rankrender-100">
                See how RankRender's Professional plan can optimize your sportsbook's 
                live odds and betting content for maximum search visibility.
              </p>
              <div className="mt-10 flex items-center justify-center gap-x-6">
                <Button href="/register" color="white">
                  Start Professional Plan
                </Button>
                <Button href="/contact" variant="outline" color="white">
                  Schedule Demo
                </Button>
              </div>
            </div>
          </Container>
        </div>
      </main>
      <Footer />
    </>
  )
}
