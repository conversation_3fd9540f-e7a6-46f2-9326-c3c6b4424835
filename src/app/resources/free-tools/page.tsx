import { type Metadata } from 'next'
import { Container } from '@/components/Container'
import { Header } from '@/components/Header'
import { Footer } from '@/components/Footer'
import { Button } from '@/components/Button'

export const metadata: Metadata = {
  title: 'Free SEO Tools',
  description: 'Free SEO audit tools and performance analyzers for JavaScript-heavy websites and applications.',
}

const tools = [
  {
    name: 'JavaScript SEO Analyzer',
    description: 'Analyze how search engines see your JavaScript-heavy website and identify optimization opportunities.',
    icon: '🔍',
    features: [
      'JavaScript rendering analysis',
      'Content accessibility check',
      'Performance impact assessment',
      'Mobile optimization review'
    ],
    status: 'Available',
    link: '#analyzer'
  },
  {
    name: 'Social Media Preview Tester',
    description: 'Test how your pages appear when shared on Facebook, Twitter, LinkedIn, and other social platforms.',
    icon: '📱',
    features: [
      'Facebook Open Graph preview',
      'Twitter Card validation',
      'LinkedIn preview check',
      'WhatsApp link preview'
    ],
    status: 'Available',
    link: '#social-preview'
  },
  {
    name: 'AI Search Readiness Checker',
    description: 'Evaluate how well your content is optimized for AI search engines like ChatGPT, Claude, and Perplexity.',
    icon: '🤖',
    features: [
      'Structured data validation',
      'Content accessibility for AI',
      'Citation-readiness score',
      'AI optimization recommendations'
    ],
    status: 'Coming Soon',
    link: '#ai-checker'
  },
  {
    name: 'Page Speed Impact Calculator',
    description: 'Calculate the SEO impact of your page speed and get recommendations for improvement.',
    icon: '⚡',
    features: [
      'Core Web Vitals analysis',
      'SEO impact assessment',
      'Performance recommendations',
      'Mobile speed optimization'
    ],
    status: 'Available',
    link: '#speed-calculator'
  },
  {
    name: 'iGaming Compliance Checker',
    description: 'Specialized tool for iGaming platforms to check SEO compliance across different gambling jurisdictions.',
    icon: '⚖️',
    features: [
      'Multi-jurisdiction compliance',
      'Content regulation check',
      'Advertising guidelines review',
      'Risk assessment report'
    ],
    status: 'Beta',
    link: '#compliance-checker'
  },
  {
    name: 'E-commerce SEO Auditor',
    description: 'Comprehensive SEO audit tool specifically designed for e-commerce platforms and product catalogs.',
    icon: '🛒',
    features: [
      'Product page optimization',
      'Category structure analysis',
      'Schema markup validation',
      'Conversion optimization tips'
    ],
    status: 'Coming Soon',
    link: '#ecommerce-auditor'
  }
]

export default function FreeTools() {
  return (
    <>
      <Header />
      <main>
        {/* Hero Section */}
        <div className="bg-white py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl text-center">
              <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl">
                Free SEO Tools
              </h1>
              <p className="mt-6 text-lg leading-8 text-gray-600">
                Powerful, free SEO audit tools and performance analyzers designed for modern JavaScript-heavy websites. 
                Get instant insights and optimization recommendations.
              </p>
              <div className="mt-10 flex items-center justify-center gap-x-6">
                <Button href="#tools">Explore Tools</Button>
                <Button href="/register" variant="outline">
                  Get Full Platform Access
                </Button>
              </div>
            </div>
          </Container>
        </div>

        {/* Tools Grid */}
        <div id="tools" className="bg-gray-50 py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl lg:text-center">
              <h2 className="text-base font-semibold leading-7 text-rankrender-600">Free SEO tools</h2>
              <p className="mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                Everything you need to audit and optimize
              </p>
              <p className="mt-6 text-lg leading-8 text-gray-600">
                Our free tools help you identify SEO issues and opportunities across different types of websites and applications.
              </p>
            </div>
            <div className="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-x-8 gap-y-16 sm:mt-20 lg:mx-0 lg:max-w-none lg:grid-cols-2">
              {tools.map((tool) => (
                <div key={tool.name} className="relative rounded-2xl bg-white p-8 shadow-lg ring-1 ring-gray-200">
                  <div className="flex items-center gap-x-3">
                    <span className="text-3xl">{tool.icon}</span>
                    <div>
                      <h3 className="text-lg font-semibold leading-8 tracking-tight text-gray-900">
                        {tool.name}
                      </h3>
                      <div className="flex items-center gap-x-2 mt-1">
                        <span className={`inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ${
                          tool.status === 'Available' 
                            ? 'bg-green-100 text-green-700'
                            : tool.status === 'Beta'
                            ? 'bg-yellow-100 text-yellow-700'
                            : 'bg-gray-100 text-gray-700'
                        }`}>
                          {tool.status}
                        </span>
                      </div>
                    </div>
                  </div>
                  <p className="mt-4 text-base leading-7 text-gray-600">
                    {tool.description}
                  </p>
                  <ul className="mt-6 space-y-2">
                    {tool.features.map((feature, index) => (
                      <li key={index} className="flex items-center text-sm text-gray-600">
                        <svg className="mr-2 h-4 w-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clipRule="evenodd" />
                        </svg>
                        {feature}
                      </li>
                    ))}
                  </ul>
                  <div className="mt-8">
                    {tool.status === 'Available' ? (
                      <Button href={tool.link} className="w-full">
                        Use Tool
                      </Button>
                    ) : tool.status === 'Beta' ? (
                      <Button href={tool.link} variant="outline" className="w-full">
                        Try Beta
                      </Button>
                    ) : (
                      <Button href="/register" variant="outline" className="w-full">
                        Get Notified
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </Container>
        </div>

        {/* Tool Demos */}
        <div className="bg-white py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl lg:text-center">
              <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                Try our tools now
              </h2>
              <p className="mt-6 text-lg leading-8 text-gray-600">
                Get instant SEO insights for your website with our free tools.
              </p>
            </div>

            {/* JavaScript SEO Analyzer Demo */}
            <div id="analyzer" className="mx-auto mt-16 max-w-4xl">
              <div className="rounded-2xl bg-gray-50 p-8">
                <div className="flex items-center gap-x-3 mb-6">
                  <span className="text-2xl">🔍</span>
                  <h3 className="text-xl font-semibold text-gray-900">JavaScript SEO Analyzer</h3>
                </div>
                <div className="space-y-4">
                  <div>
                    <label htmlFor="url-input" className="block text-sm font-medium leading-6 text-gray-900">
                      Enter your website URL
                    </label>
                    <div className="mt-2 flex rounded-md shadow-sm">
                      <input
                        type="url"
                        name="url"
                        id="url-input"
                        className="block w-full rounded-l-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-rankrender-600 sm:text-sm sm:leading-6"
                        placeholder="https://example.com"
                      />
                      <button
                        type="button"
                        className="relative -ml-px inline-flex items-center gap-x-1.5 rounded-r-md px-3 py-2 text-sm font-semibold text-white bg-rankrender-600 hover:bg-rankrender-500"
                      >
                        Analyze
                      </button>
                    </div>
                  </div>
                  <p className="text-sm text-gray-600">
                    This tool will analyze how search engines render your JavaScript content and provide optimization recommendations.
                  </p>
                </div>
              </div>
            </div>

            {/* Social Media Preview Tester Demo */}
            <div id="social-preview" className="mx-auto mt-8 max-w-4xl">
              <div className="rounded-2xl bg-gray-50 p-8">
                <div className="flex items-center gap-x-3 mb-6">
                  <span className="text-2xl">📱</span>
                  <h3 className="text-xl font-semibold text-gray-900">Social Media Preview Tester</h3>
                </div>
                <div className="space-y-4">
                  <div>
                    <label htmlFor="social-url-input" className="block text-sm font-medium leading-6 text-gray-900">
                      Test social media previews
                    </label>
                    <div className="mt-2 flex rounded-md shadow-sm">
                      <input
                        type="url"
                        name="social-url"
                        id="social-url-input"
                        className="block w-full rounded-l-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-rankrender-600 sm:text-sm sm:leading-6"
                        placeholder="https://example.com/page"
                      />
                      <button
                        type="button"
                        className="relative -ml-px inline-flex items-center gap-x-1.5 rounded-r-md px-3 py-2 text-sm font-semibold text-white bg-rankrender-600 hover:bg-rankrender-500"
                      >
                        Test Previews
                      </button>
                    </div>
                  </div>
                  <p className="text-sm text-gray-600">
                    See how your pages appear when shared on Facebook, Twitter, LinkedIn, and other social platforms.
                  </p>
                </div>
              </div>
            </div>

            {/* Page Speed Calculator Demo */}
            <div id="speed-calculator" className="mx-auto mt-8 max-w-4xl">
              <div className="rounded-2xl bg-gray-50 p-8">
                <div className="flex items-center gap-x-3 mb-6">
                  <span className="text-2xl">⚡</span>
                  <h3 className="text-xl font-semibold text-gray-900">Page Speed Impact Calculator</h3>
                </div>
                <div className="space-y-4">
                  <div>
                    <label htmlFor="speed-url-input" className="block text-sm font-medium leading-6 text-gray-900">
                      Analyze page speed impact
                    </label>
                    <div className="mt-2 flex rounded-md shadow-sm">
                      <input
                        type="url"
                        name="speed-url"
                        id="speed-url-input"
                        className="block w-full rounded-l-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-rankrender-600 sm:text-sm sm:leading-6"
                        placeholder="https://example.com"
                      />
                      <button
                        type="button"
                        className="relative -ml-px inline-flex items-center gap-x-1.5 rounded-r-md px-3 py-2 text-sm font-semibold text-white bg-rankrender-600 hover:bg-rankrender-500"
                      >
                        Calculate Impact
                      </button>
                    </div>
                  </div>
                  <p className="text-sm text-gray-600">
                    Calculate how your page speed affects SEO rankings and get specific recommendations for improvement.
                  </p>
                </div>
              </div>
            </div>
          </Container>
        </div>

        {/* Upgrade CTA */}
        <div className="bg-rankrender-600 py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl text-center">
              <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl">
                Need more advanced features?
              </h2>
              <p className="mx-auto mt-6 max-w-xl text-lg leading-8 text-rankrender-100">
                Upgrade to RankRender Pro for automated optimization, real-time monitoring, and advanced analytics.
              </p>
              <div className="mt-10 flex items-center justify-center gap-x-6">
                <Button href="/register" color="white">
                  Start Free Trial
                </Button>
                <Button href="/pricing" variant="outline" color="white">
                  View Pricing
                </Button>
              </div>
            </div>
          </Container>
        </div>
      </main>
      <Footer />
    </>
  )
}
