import { type Metadata } from 'next'
import { Container } from '@/components/Container'
import { Header } from '@/components/Header'
import { Footer } from '@/components/Footer'
import { But<PERSON> } from '@/components/Button'

export const metadata: Metadata = {
  title: 'AI Search Optimization',
  description: 'Get discovered by <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and other AI search engines. Future-proof your SEO for the age of AI-powered search.',
}

export default function AISearchOptimization() {
  return (
    <>
      <Header />
      <main>
        {/* Hero Section */}
        <div className="bg-white py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl text-center">
              <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl">
                AI Search Optimization
              </h1>
              <p className="mt-6 text-lg leading-8 text-gray-600">
                Get discovered by <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and other AI search engines. 
                Future-proof your SEO strategy for the age of AI-powered search.
              </p>
              <div className="mt-10 flex items-center justify-center gap-x-6">
                <Button href="/register">Start Free Trial</Button>
                <Button href="/contact" variant="outline">
                  Learn More
                </Button>
              </div>
            </div>
          </Container>
        </div>

        {/* AI Search Landscape */}
        <div className="bg-gray-50 py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl lg:text-center">
              <h2 className="text-base font-semibold leading-7 text-rankrender-600">The future of search</h2>
              <p className="mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                AI is changing how people discover content
              </p>
              <p className="mt-6 text-lg leading-8 text-gray-600">
                Traditional search engines are just the beginning. AI-powered search tools are becoming the primary way people find information.
              </p>
            </div>
            <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
              <dl className="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-3">
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900">
                    <div className="h-10 w-10 flex items-center justify-center rounded-lg bg-rankrender-600 text-white">
                      <span className="text-lg">🤖</span>
                    </div>
                    ChatGPT & GPT Models
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      OpenAI's ChatGPT and other GPT models are increasingly used for research and discovery. Ensure your content is accessible to these AI systems.
                    </p>
                  </dd>
                </div>
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900">
                    <div className="h-10 w-10 flex items-center justify-center rounded-lg bg-rankrender-600 text-white">
                      <span className="text-lg">🔍</span>
                    </div>
                    Perplexity & Claude
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      AI search engines like Perplexity and Anthropic's Claude are becoming primary research tools. Make sure they can find and cite your content.
                    </p>
                  </dd>
                </div>
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900">
                    <div className="h-10 w-10 flex items-center justify-center rounded-lg bg-rankrender-600 text-white">
                      <span className="text-lg">🚀</span>
                    </div>
                    Future AI Tools
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      New AI search tools are launching constantly. Future-proof your content strategy to be discoverable by the next generation of AI.
                    </p>
                  </dd>
                </div>
              </dl>
            </div>
          </Container>
        </div>

        {/* How RankRender Helps */}
        <div className="bg-white py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl lg:text-center">
              <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                How RankRender optimizes for AI search
              </h2>
              <p className="mt-6 text-lg leading-8 text-gray-600">
                Our advanced optimization techniques ensure your content is discoverable by both traditional search engines and AI-powered tools.
              </p>
            </div>
            <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
              <dl className="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-2">
                <div className="flex flex-col">
                  <dt className="text-lg font-semibold leading-8 tracking-tight text-gray-900">
                    🧠 Structured Data Optimization
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      We ensure your content is properly structured with schema markup and semantic HTML that AI systems can easily understand and process.
                    </p>
                  </dd>
                </div>
                <div className="flex flex-col">
                  <dt className="text-lg font-semibold leading-8 tracking-tight text-gray-900">
                    📝 Content Accessibility
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      JavaScript-heavy content is made accessible to AI crawlers, ensuring your dynamic content can be discovered and cited by AI tools.
                    </p>
                  </dd>
                </div>
                <div className="flex flex-col">
                  <dt className="text-lg font-semibold leading-8 tracking-tight text-gray-900">
                    🔗 Citation-Ready Format
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      Your content is optimized in formats that AI systems prefer for citations, increasing the likelihood of being referenced in AI-generated responses.
                    </p>
                  </dd>
                </div>
                <div className="flex flex-col">
                  <dt className="text-lg font-semibold leading-8 tracking-tight text-gray-900">
                    ⚡ Real-Time Updates
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      As your content changes, our system ensures AI crawlers always have access to the most current version of your information.
                    </p>
                  </dd>
                </div>
              </dl>
            </div>
          </Container>
        </div>

        {/* Benefits for Different Industries */}
        <div className="bg-gray-50 py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl lg:text-center">
              <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                AI search benefits across industries
              </h2>
            </div>
            <div className="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-x-8 gap-y-16 sm:mt-20 lg:mx-0 lg:max-w-none lg:grid-cols-3">
              <div className="flex flex-col">
                <h3 className="text-lg font-semibold leading-8 tracking-tight text-gray-900">
                  🎰 iGaming Platforms
                </h3>
                <p className="mt-2 text-base leading-7 text-gray-600">
                  Get your casino games, sports betting odds, and poker tournaments discovered when users ask AI about gaming options and strategies.
                </p>
              </div>
              <div className="flex flex-col">
                <h3 className="text-lg font-semibold leading-8 tracking-tight text-gray-900">
                  🛒 E-commerce Sites
                </h3>
                <p className="mt-2 text-base leading-7 text-gray-600">
                  Ensure your products appear when customers ask AI tools for shopping recommendations and product comparisons.
                </p>
              </div>
              <div className="flex flex-col">
                <h3 className="text-lg font-semibold leading-8 tracking-tight text-gray-900">
                  💻 SaaS Platforms
                </h3>
                <p className="mt-2 text-base leading-7 text-gray-600">
                  Make your software features and documentation discoverable when users research solutions for their business needs.
                </p>
              </div>
              <div className="flex flex-col">
                <h3 className="text-lg font-semibold leading-8 tracking-tight text-gray-900">
                  📰 Content Publishers
                </h3>
                <p className="mt-2 text-base leading-7 text-gray-600">
                  Increase the chances of your articles being cited and referenced in AI-generated responses to user queries.
                </p>
              </div>
              <div className="flex flex-col">
                <h3 className="text-lg font-semibold leading-8 tracking-tight text-gray-900">
                  🏥 Healthcare & Finance
                </h3>
                <p className="mt-2 text-base leading-7 text-gray-600">
                  Ensure your authoritative content is discoverable while maintaining compliance with industry regulations.
                </p>
              </div>
              <div className="flex flex-col">
                <h3 className="text-lg font-semibold leading-8 tracking-tight text-gray-900">
                  🎓 Education & Training
                </h3>
                <p className="mt-2 text-base leading-7 text-gray-600">
                  Make your educational content and courses discoverable when learners ask AI for learning recommendations.
                </p>
              </div>
            </div>
          </Container>
        </div>

        {/* CTA Section */}
        <div className="bg-rankrender-600 py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl text-center">
              <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl">
                Ready for the AI search revolution?
              </h2>
              <p className="mx-auto mt-6 max-w-xl text-lg leading-8 text-rankrender-100">
                Don't get left behind as AI transforms how people discover content. Start optimizing for AI search today.
              </p>
              <div className="mt-10 flex items-center justify-center gap-x-6">
                <Button href="/register" color="white">
                  Start Free Trial
                </Button>
                <Button href="/contact" variant="outline" color="white">
                  Talk to AI SEO Expert
                </Button>
              </div>
            </div>
          </Container>
        </div>
      </main>
      <Footer />
    </>
  )
}
