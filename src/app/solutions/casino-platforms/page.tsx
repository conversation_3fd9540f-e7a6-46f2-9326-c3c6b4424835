import { type Metadata } from 'next'
import { Container } from '@/components/Container'
import { Header } from '@/components/Header'
import { Footer } from '@/components/Footer'
import { Button } from '@/components/Button'

export const metadata: Metadata = {
  title: 'Casino Platform SEO Solutions',
  description: 'Specialized SEO optimization for online casino platforms. Boost visibility for your slot games, table games, and live dealer content.',
}

export default function CasinoPlatforms() {
  return (
    <>
      <Header />
      <main>
        {/* Hero Section */}
        <div className="bg-white py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl text-center">
              <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl">
                Casino Platform SEO Solutions
              </h1>
              <p className="mt-6 text-lg leading-8 text-gray-600">
                Specialized SEO optimization for online casino platforms. Boost visibility for your slot games,
                table games, live dealer content, and promotional offers.
              </p>
              <div className="mt-10 flex items-center justify-center gap-x-6">
                <Button href="/register">Get Started Free</Button>
                <Button href="/contact" variant="outline">
                  Get Casino Demo
                </Button>
              </div>
            </div>
          </Container>
        </div>

        {/* Casino SEO Challenges */}
        <div className="bg-gray-50 py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl lg:text-center">
              <h2 className="text-base font-semibold leading-7 text-rankrender-600">Casino SEO challenges</h2>
              <p className="mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                Unique challenges for casino platforms
              </p>
              <p className="mt-6 text-lg leading-8 text-gray-600">
                Casino platforms face specific SEO challenges that traditional optimization tools can't handle.
              </p>
            </div>
            <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
              <dl className="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-2">
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-red-600">
                    <span className="text-2xl">🎰</span>
                    Game Library Invisibility
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      Slot games, table games, and live dealer content built with JavaScript are invisible to search engines,
                      missing out on valuable organic traffic from players searching for specific games.
                    </p>
                  </dd>
                </div>
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-red-600">
                    <span className="text-2xl">🎁</span>
                    Promotion Tracking Issues
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      Casino bonuses, free spins, and promotional offers change frequently but search engines can't keep up,
                      resulting in outdated or missing promotional content in search results.
                    </p>
                  </dd>
                </div>
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-red-600">
                    <span className="text-2xl">⚖️</span>
                    Compliance Complexity
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      Gambling regulations across different jurisdictions make it challenging to optimize content
                      without risking compliance violations or regulatory issues.
                    </p>
                  </dd>
                </div>
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-red-600">
                    <span className="text-2xl">📱</span>
                    Mobile Gaming Optimization
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      Mobile casino experiences are heavily JavaScript-dependent, making it difficult for search engines
                      to understand and rank mobile gaming content properly.
                    </p>
                  </dd>
                </div>
              </dl>
            </div>
          </Container>
        </div>

        {/* RankRender Solutions */}
        <div className="bg-white py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl lg:text-center">
              <h2 className="text-base font-semibold leading-7 text-rankrender-600">RankRender solutions</h2>
              <p className="mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                How we solve casino SEO challenges
              </p>
              <p className="mt-6 text-lg leading-8 text-gray-600">
                Our specialized casino optimization ensures every game, promotion, and feature is discoverable by search engines.
              </p>
            </div>
            <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
              <dl className="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-2">
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-green-600">
                    <svg className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Complete Game Library Indexing
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      Every slot game, table game, and live dealer option gets properly indexed with detailed metadata,
                      game features, RTP information, and provider details for maximum search visibility.
                    </p>
                  </dd>
                </div>
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-green-600">
                    <svg className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Real-Time Promotion Tracking
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      Casino bonuses, free spins, and promotional offers are automatically tracked and optimized
                      as they change, ensuring search engines always have current promotional information.
                    </p>
                  </dd>
                </div>
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-green-600">
                    <svg className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Compliance-First Optimization
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      Built-in compliance monitoring for major gambling jurisdictions including UK, Malta, Curacao,
                      and Gibraltar ensures your SEO optimizations meet regulatory requirements.
                    </p>
                  </dd>
                </div>
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-green-600">
                    <svg className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Mobile Casino Optimization
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      Specialized mobile optimization ensures your mobile casino games and features are properly
                      indexed and ranked for mobile search queries and app store discovery.
                    </p>
                  </dd>
                </div>
              </dl>
            </div>
          </Container>
        </div>

        {/* Game Categories */}
        <div className="bg-gray-50 py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl lg:text-center">
              <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                Optimize every game category
              </h2>
              <p className="mt-6 text-lg leading-8 text-gray-600">
                Specialized optimization for different types of casino content and gaming experiences.
              </p>
            </div>
            <div className="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-x-8 gap-y-16 sm:mt-20 lg:mx-0 lg:max-w-none lg:grid-cols-3">
              <div className="flex flex-col items-start">
                <div className="flex items-center gap-x-3">
                  <span className="text-3xl">🎰</span>
                  <h3 className="text-lg font-semibold leading-8 tracking-tight text-gray-900">
                    Slot Games
                  </h3>
                </div>
                <p className="mt-4 text-base leading-7 text-gray-600">
                  Optimize individual slot games with detailed metadata including themes, features, RTP, volatility,
                  and provider information for better search discovery.
                </p>
                <ul className="mt-4 space-y-2 text-sm text-gray-600">
                  <li>• Game theme and storyline optimization</li>
                  <li>• RTP and volatility information</li>
                  <li>• Bonus feature descriptions</li>
                  <li>• Provider and software details</li>
                </ul>
              </div>
              <div className="flex flex-col items-start">
                <div className="flex items-center gap-x-3">
                  <span className="text-3xl">🃏</span>
                  <h3 className="text-lg font-semibold leading-8 tracking-tight text-gray-900">
                    Table Games
                  </h3>
                </div>
                <p className="mt-4 text-base leading-7 text-gray-600">
                  Enhance discoverability of blackjack, roulette, baccarat, and other table games with
                  detailed rule variations and betting options.
                </p>
                <ul className="mt-4 space-y-2 text-sm text-gray-600">
                  <li>• Game rule variations</li>
                  <li>• Betting limits and options</li>
                  <li>• Strategy guide integration</li>
                  <li>• Multi-language support</li>
                </ul>
              </div>
              <div className="flex flex-col items-start">
                <div className="flex items-center gap-x-3">
                  <span className="text-3xl">🎥</span>
                  <h3 className="text-lg font-semibold leading-8 tracking-tight text-gray-900">
                    Live Dealer Games
                  </h3>
                </div>
                <p className="mt-4 text-base leading-7 text-gray-600">
                  Optimize live casino experiences with dealer information, studio details,
                  and real-time availability for enhanced search visibility.
                </p>
                <ul className="mt-4 space-y-2 text-sm text-gray-600">
                  <li>• Live studio information</li>
                  <li>• Dealer profiles and schedules</li>
                  <li>• Real-time availability</li>
                  <li>• Interactive feature details</li>
                </ul>
              </div>
            </div>
          </Container>
        </div>

        {/* Results & Benefits */}
        <div className="bg-white py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl lg:text-center">
              <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                Results you can expect
              </h2>
              <p className="mt-6 text-lg leading-8 text-gray-600">
                Casino platforms using RankRender see significant improvements in search visibility and player acquisition.
              </p>
            </div>
            <div className="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-x-8 gap-y-16 sm:mt-20 lg:mx-0 lg:max-w-none lg:grid-cols-4">
              <div className="flex flex-col items-center text-center">
                <div className="text-4xl font-bold tracking-tight text-rankrender-600">
                  300%
                </div>
                <div className="text-sm leading-6 text-gray-600 mt-2">
                  Increase in game page visibility
                </div>
                <p className="mt-4 text-sm text-gray-500">
                  Individual slot and table games rank higher in search results
                </p>
              </div>
              <div className="flex flex-col items-center text-center">
                <div className="text-4xl font-bold tracking-tight text-rankrender-600">
                  85%
                </div>
                <div className="text-sm leading-6 text-gray-600 mt-2">
                  Faster promotion indexing
                </div>
                <p className="mt-4 text-sm text-gray-500">
                  Casino bonuses and offers appear in search within hours
                </p>
              </div>
              <div className="flex flex-col items-center text-center">
                <div className="text-4xl font-bold tracking-tight text-rankrender-600">
                  200%
                </div>
                <div className="text-sm leading-6 text-gray-600 mt-2">
                  More organic player acquisition
                </div>
                <p className="mt-4 text-sm text-gray-500">
                  Increased organic traffic from players searching for games
                </p>
              </div>
              <div className="flex flex-col items-center text-center">
                <div className="text-4xl font-bold tracking-tight text-rankrender-600">
                  100%
                </div>
                <div className="text-sm leading-6 text-gray-600 mt-2">
                  Compliance maintained
                </div>
                <p className="mt-4 text-sm text-gray-500">
                  All optimizations meet gambling regulatory requirements
                </p>
              </div>
            </div>
          </Container>
        </div>

        {/* CTA Section */}
        <div className="bg-rankrender-600 py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl text-center">
              <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl">
                Ready to boost your casino's SEO?
              </h2>
              <p className="mx-auto mt-6 max-w-xl text-lg leading-8 text-rankrender-100">
                Join leading casino platforms that trust RankRender to optimize their game libraries and attract more players.
              </p>
              <div className="mt-10 flex items-center justify-center gap-x-6">
                <Button href="/register" color="white">
                  Get Started Free
                </Button>
                <Button href="/contact" variant="outline" color="white">
                  Get Casino Demo
                </Button>
              </div>
            </div>
          </Container>
        </div>
      </main>
      <Footer />
    </>
  )
}
