import { type Metadata } from 'next'
import { Container } from '@/components/Container'
import { Header } from '@/components/Header'
import { Footer } from '@/components/Footer'
import { Button } from '@/components/Button'

export const metadata: Metadata = {
  title: 'E-commerce SEO Solutions',
  description: 'Specialized SEO optimization for e-commerce platforms. Optimize product catalogs, dynamic pricing, and shopping experiences.',
}

export default function EcommerceSolutions() {
  return (
    <>
      <Header />
      <main>
        {/* Hero Section */}
        <div className="bg-white py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl text-center">
              <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl">
                E-commerce SEO Solutions
              </h1>
              <p className="mt-6 text-lg leading-8 text-gray-600">
                Specialized SEO optimization for e-commerce platforms. Optimize product catalogs,
                dynamic pricing, inventory updates, and shopping experiences for maximum search visibility.
              </p>
              <div className="mt-10 flex items-center justify-center gap-x-6">
                <Button href="/register">Get Started Free</Button>
                <Button href="/contact" variant="outline">
                  Get E-commerce Demo
                </Button>
              </div>
            </div>
          </Container>
        </div>

        {/* E-commerce SEO Challenges */}
        <div className="bg-gray-50 py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl lg:text-center">
              <h2 className="text-base font-semibold leading-7 text-rankrender-600">E-commerce SEO challenges</h2>
              <p className="mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                Unique challenges for online stores
              </p>
              <p className="mt-6 text-lg leading-8 text-gray-600">
                E-commerce platforms face specific SEO challenges related to product catalogs, inventory management, and dynamic content.
              </p>
            </div>
            <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
              <dl className="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-2">
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-red-600">
                    <span className="text-2xl">📦</span>
                    Product Catalog Invisibility
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      Product pages built with JavaScript frameworks are often invisible to search engines,
                      missing valuable organic traffic from product searches and comparison queries.
                    </p>
                  </dd>
                </div>
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-red-600">
                    <span className="text-2xl">💰</span>
                    Dynamic Pricing Issues
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      Real-time pricing updates, discounts, and promotional offers change constantly but search engines
                      can't track these updates, resulting in outdated pricing information in search results.
                    </p>
                  </dd>
                </div>
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-red-600">
                    <span className="text-2xl">📊</span>
                    Inventory Tracking Problems
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      Stock levels, product availability, and variant options are dynamically generated,
                      making it difficult for search engines to understand what's actually available for purchase.
                    </p>
                  </dd>
                </div>
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-red-600">
                    <span className="text-2xl">🔍</span>
                    Search & Filter Complexity
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      Advanced search filters, category navigation, and faceted search results are JavaScript-heavy
                      and often break SEO, preventing search engines from discovering filtered product pages.
                    </p>
                  </dd>
                </div>
              </dl>
            </div>
          </Container>
        </div>

        {/* RankRender Solutions */}
        <div className="bg-white py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl lg:text-center">
              <h2 className="text-base font-semibold leading-7 text-rankrender-600">RankRender solutions</h2>
              <p className="mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                How we optimize e-commerce platforms
              </p>
              <p className="mt-6 text-lg leading-8 text-gray-600">
                Our specialized e-commerce optimization ensures every product, category, and shopping feature is discoverable.
              </p>
            </div>
            <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
              <dl className="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-2">
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-green-600">
                    <svg className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Complete Product Indexing
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      Every product page is properly indexed with detailed metadata including descriptions, specifications,
                      pricing, availability, and customer reviews for maximum search visibility.
                    </p>
                  </dd>
                </div>
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-green-600">
                    <svg className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Real-Time Price Optimization
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      Dynamic pricing, discounts, and promotional offers are automatically tracked and optimized
                      as they change, ensuring search engines always have current pricing information.
                    </p>
                  </dd>
                </div>
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-green-600">
                    <svg className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Smart Inventory Tracking
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      Stock levels and product availability are optimized in real-time, helping customers find available products
                      and preventing search engines from indexing out-of-stock items.
                    </p>
                  </dd>
                </div>
                <div className="flex flex-col">
                  <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-green-600">
                    <svg className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Advanced Search SEO
                  </dt>
                  <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                    <p className="flex-auto">
                      Search filters, category pages, and faceted navigation are optimized to create SEO-friendly URLs
                      and ensure filtered product pages are properly indexed and ranked.
                    </p>
                  </dd>
                </div>
              </dl>
            </div>
          </Container>
        </div>

        {/* E-commerce Features */}
        <div className="bg-gray-50 py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl lg:text-center">
              <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                Optimize every e-commerce feature
              </h2>
              <p className="mt-6 text-lg leading-8 text-gray-600">
                Comprehensive optimization for all aspects of your online store and shopping experience.
              </p>
            </div>
            <div className="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-x-8 gap-y-16 sm:mt-20 lg:mx-0 lg:max-w-none lg:grid-cols-3">
              <div className="flex flex-col items-start">
                <div className="flex items-center gap-x-3">
                  <span className="text-3xl">🛍️</span>
                  <h3 className="text-lg font-semibold leading-8 tracking-tight text-gray-900">
                    Product Pages
                  </h3>
                </div>
                <p className="mt-4 text-base leading-7 text-gray-600">
                  Optimize individual product pages with rich snippets, detailed descriptions, and customer reviews.
                </p>
                <ul className="mt-4 space-y-2 text-sm text-gray-600">
                  <li>• Product schema markup</li>
                  <li>• Review and rating optimization</li>
                  <li>• Image and video SEO</li>
                  <li>• Related product suggestions</li>
                </ul>
              </div>
              <div className="flex flex-col items-start">
                <div className="flex items-center gap-x-3">
                  <span className="text-3xl">📂</span>
                  <h3 className="text-lg font-semibold leading-8 tracking-tight text-gray-900">
                    Category Navigation
                  </h3>
                </div>
                <p className="mt-4 text-base leading-7 text-gray-600">
                  Enhance category pages and navigation structures for better search engine understanding and user experience.
                </p>
                <ul className="mt-4 space-y-2 text-sm text-gray-600">
                  <li>• Category page optimization</li>
                  <li>• Breadcrumb navigation</li>
                  <li>• Faceted search SEO</li>
                  <li>• Filter-friendly URLs</li>
                </ul>
              </div>
              <div className="flex flex-col items-start">
                <div className="flex items-center gap-x-3">
                  <span className="text-3xl">🛒</span>
                  <h3 className="text-lg font-semibold leading-8 tracking-tight text-gray-900">
                    Shopping Experience
                  </h3>
                </div>
                <p className="mt-4 text-base leading-7 text-gray-600">
                  Optimize checkout flows, shopping carts, and user account features for better conversion and discovery.
                </p>
                <ul className="mt-4 space-y-2 text-sm text-gray-600">
                  <li>• Checkout page optimization</li>
                  <li>• User account SEO</li>
                  <li>• Wishlist and favorites</li>
                  <li>• Order tracking pages</li>
                </ul>
              </div>
            </div>
          </Container>
        </div>

        {/* Results & Benefits */}
        <div className="bg-white py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl lg:text-center">
              <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                Proven results for e-commerce stores
              </h2>
              <p className="mt-6 text-lg leading-8 text-gray-600">
                E-commerce platforms using RankRender see significant improvements in product visibility and sales.
              </p>
            </div>
            <div className="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-x-8 gap-y-16 sm:mt-20 lg:mx-0 lg:max-w-none lg:grid-cols-4">
              <div className="flex flex-col items-center text-center">
                <div className="text-4xl font-bold tracking-tight text-rankrender-600">
                  320%
                </div>
                <div className="text-sm leading-6 text-gray-600 mt-2">
                  Increase in product page visibility
                </div>
                <p className="mt-4 text-sm text-gray-500">
                  Individual products rank higher in search results
                </p>
              </div>
              <div className="flex flex-col items-center text-center">
                <div className="text-4xl font-bold tracking-tight text-rankrender-600">
                  90%
                </div>
                <div className="text-sm leading-6 text-gray-600 mt-2">
                  Faster product indexing
                </div>
                <p className="mt-4 text-sm text-gray-500">
                  New products appear in search within hours
                </p>
              </div>
              <div className="flex flex-col items-center text-center">
                <div className="text-4xl font-bold tracking-tight text-rankrender-600">
                  180%
                </div>
                <div className="text-sm leading-6 text-gray-600 mt-2">
                  More organic sales
                </div>
                <p className="mt-4 text-sm text-gray-500">
                  Increased revenue from organic search traffic
                </p>
              </div>
              <div className="flex flex-col items-center text-center">
                <div className="text-4xl font-bold tracking-tight text-rankrender-600">
                  95%
                </div>
                <div className="text-sm leading-6 text-gray-600 mt-2">
                  Inventory accuracy
                </div>
                <p className="mt-4 text-sm text-gray-500">
                  Real-time stock levels in search results
                </p>
              </div>
            </div>
          </Container>
        </div>

        {/* CTA Section */}
        <div className="bg-rankrender-600 py-24 sm:py-32">
          <Container>
            <div className="mx-auto max-w-2xl text-center">
              <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl">
                Ready to boost your e-commerce SEO?
              </h2>
              <p className="mx-auto mt-6 max-w-xl text-lg leading-8 text-rankrender-100">
                Join leading e-commerce brands that trust RankRender to optimize their product catalogs and drive more sales.
              </p>
              <div className="mt-10 flex items-center justify-center gap-x-6">
                <Button href="/register" color="white">
                  Get Started Free
                </Button>
                <Button href="/contact" variant="outline" color="white">
                  Get E-commerce Demo
                </Button>
              </div>
            </div>
          </Container>
        </div>
      </main>
      <Footer />
    </>
  )
}
