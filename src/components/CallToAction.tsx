import Image from 'next/image'

import { Button } from '@/components/Button'
import { Container } from '@/components/Container'
import backgroundImage from '@/images/rankrender_bg_2.png'

export function CallToAction() {
  return (
    <section
      id="get-started-today"
      className="relative overflow-hidden bg-rankrender-600 py-32"
    >
      <Image
        className="absolute top-1/2 left-1/2 max-w-none -translate-x-1/2 -translate-y-1/2"
        src={backgroundImage}
        alt=""
        width={2347}
        height={1244}
        unoptimized
      />
      <Container className="relative">
        <div className="mx-auto max-w-2xl text-center">
          <h2 className="font-display text-4xl tracking-tight text-white sm:text-5xl">
            Ready to boost your SEO rankings?
          </h2>
          <p className="mt-6 text-xl tracking-tight text-white">
            Join 50+ successful companies who trust RankRender to boost their search rankings and drive more organic traffic.
          </p>
          <div className="mt-10 flex flex-col sm:flex-row justify-center gap-4">
            <Button href="/register" color="white" className="text-lg px-8 py-4">
              Start Free Trial
              <span className="ml-2">→</span>
            </Button>
            <Button href="/pricing" variant="outline" color="white" className="text-lg px-8 py-4">
              View Pricing
            </Button>
          </div>
          <div className="mt-8 text-sm text-rankrender-100">
            <p>✓ No credit card required • ✓ 1,000 free renders monthly • ✓ Setup in 5 minutes</p>
          </div>
        </div>
      </Container>
    </section>
  )
}
