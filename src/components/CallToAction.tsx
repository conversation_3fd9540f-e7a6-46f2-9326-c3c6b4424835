import Image from 'next/image'

import { But<PERSON> } from '@/components/Button'
import { Container } from '@/components/Container'
import backgroundImage from '@/images/rankrender_bg_2.png'

export function CallToAction() {
  return (
    <section
      id="get-started-today"
      className="relative overflow-hidden bg-rankrender-600 py-32"
    >
      <Image
        className="absolute top-1/2 left-1/2 max-w-none -translate-x-1/2 -translate-y-1/2"
        src={backgroundImage}
        alt=""
        width={2347}
        height={1244}
        unoptimized
      />
      <Container className="relative">
        <div className="mx-auto max-w-lg text-center">
          <h2 className="font-display text-4xl tracking-tight text-white sm:text-4xl">
            Start rendering and ranking up ☝️
          </h2>
          <p className="mt-4 text-lg tracking-tight text-white">
            It’s time to take control of your SEO. Our product is free and will get your ranks up in no time.
          </p>
          {/*<Button href="/register" color="white" className="mt-10">*/}
          {/*  Start now*/}
          {/*</Button>*/}
        </div>
      </Container>
    </section>
  )
}
