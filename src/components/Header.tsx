'use client'

import Link from 'next/link'
import {
  Popover,
  PopoverButton,
  PopoverBackdrop,
  PopoverPanel,
} from '@headlessui/react'
import clsx from 'clsx'

import { <PERSON><PERSON> } from '@/components/Button'
import { Container } from '@/components/Container'
import {LogoRankRender} from "@/components/LogoRankRender";

function MobileNavLink({
  href,
  children,
}: {
  href: string
  children: React.ReactNode
}) {
  return (
    <PopoverButton as={Link} href={href} className="block w-full px-3 py-2 text-base font-medium text-gray-700 transition-colors duration-200 hover:text-gray-900 hover:bg-gray-50 rounded-md">
      {children}
    </PopoverButton>
  )
}

function MobileNavIcon({ open }: { open: boolean }) {
  return (
    <div className="relative h-6 w-6">
      <span className="sr-only">{open ? 'Close menu' : 'Open menu'}</span>
      <div className="absolute left-1/2 top-1/2 block w-5 transform -translate-x-1/2 -translate-y-1/2">
        <span
          className={clsx(
            'absolute block h-0.5 w-5 bg-gray-700 transition-all duration-300 ease-in-out',
            open ? 'rotate-45 translate-y-0' : '-translate-y-1.5'
          )}
        />
        <span
          className={clsx(
            'absolute block h-0.5 w-5 bg-gray-700 transition-all duration-300 ease-in-out',
            open ? 'opacity-0' : 'translate-y-0'
          )}
        />
        <span
          className={clsx(
            'absolute block h-0.5 w-5 bg-gray-700 transition-all duration-300 ease-in-out',
            open ? '-rotate-45 translate-y-0' : 'translate-y-1.5'
          )}
        />
      </div>
    </div>
  )
}

function DesktopNavigation() {
  return (
    <nav className="hidden md:flex md:gap-x-8">
      <Popover className="relative">
        <PopoverButton className="flex items-center gap-x-1 text-sm font-semibold leading-6 text-slate-700 hover:text-slate-900">
          Solutions
          <svg className="h-5 w-5 flex-none text-gray-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path fillRule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z" clipRule="evenodd" />
          </svg>
        </PopoverButton>
        <PopoverPanel className="absolute left-1/2 z-10 mt-5 flex w-screen max-w-max -translate-x-1/2 px-4">
          <div className="w-screen max-w-md flex-auto overflow-hidden rounded-3xl bg-white text-sm leading-6 shadow-lg ring-1 ring-gray-900/5">
            <div className="p-4">
              <div className="group relative flex gap-x-6 rounded-lg p-4 hover:bg-gray-50">
                <div className="mt-1 flex h-11 w-11 flex-none items-center justify-center rounded-lg bg-gray-50 group-hover:bg-white">
                  <svg className="h-6 w-6 text-gray-600 group-hover:text-rankrender-600" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M21 7.5l-9-5.25L3 7.5m18 0l-9 5.25m9-5.25v9l-9 5.25M3 7.5l9 5.25M3 7.5v9l9 5.25m0-9v9" />
                  </svg>
                </div>
                <div>
                  <Link href="/solutions/igaming-operators" className="font-semibold text-gray-900">
                    For iGaming Operators
                    <span className="absolute inset-0" />
                  </Link>
                  <p className="mt-1 text-gray-600">Optimize your casino and betting platform for search engines</p>
                </div>
              </div>
              <div className="group relative flex gap-x-6 rounded-lg p-4 hover:bg-gray-50">
                <div className="mt-1 flex h-11 w-11 flex-none items-center justify-center rounded-lg bg-gray-50 group-hover:bg-white">
                  <svg className="h-6 w-6 text-gray-600 group-hover:text-rankrender-600" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M9 17.25v1.007a3 3 0 01-.879 2.122L7.5 21h9l-.621-.621A3 3 0 0115 18.257V17.25m6-12V15a2.25 2.25 0 01-2.25 2.25H5.25A2.25 2.25 0 013 15V5.25m18 0A2.25 2.25 0 0018.75 3H5.25A2.25 2.25 0 003 5.25m18 0V12a2.25 2.25 0 01-2.25 2.25H5.25A2.25 2.25 0 013 12V5.25" />
                  </svg>
                </div>
                <div>
                  <Link href="/solutions/casino-platforms" className="font-semibold text-gray-900">
                    For Casino Platforms
                    <span className="absolute inset-0" />
                  </Link>
                  <p className="mt-1 text-gray-600">Boost visibility for your online casino games and promotions</p>
                </div>
              </div>
              <div className="group relative flex gap-x-6 rounded-lg p-4 hover:bg-gray-50">
                <div className="mt-1 flex h-11 w-11 flex-none items-center justify-center rounded-lg bg-gray-50 group-hover:bg-white">
                  <svg className="h-6 w-6 text-gray-600 group-hover:text-rankrender-600" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 10.5V6a3.75 3.75 0 10-7.5 0v4.5m11.356-1.993l1.263 12c.07.665-.45 1.243-1.119 1.243H4.25a1.125 1.125 0 01-1.12-1.243l1.264-12A1.125 1.125 0 015.513 7.5h12.974c.576 0 1.059.435 1.119 1.007zM8.625 10.5a.375.375 0 11-.75 0 .375.375 0 01.75 0zm7.5 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z" />
                  </svg>
                </div>
                <div>
                  <Link href="/solutions/ecommerce" className="font-semibold text-gray-900">
                    E-commerce Optimization
                    <span className="absolute inset-0" />
                  </Link>
                  <p className="mt-1 text-gray-600">Optimize product catalogs and dynamic pricing</p>
                </div>
              </div>
              <div className="group relative flex gap-x-6 rounded-lg p-4 hover:bg-gray-50">
                <div className="mt-1 flex h-11 w-11 flex-none items-center justify-center rounded-lg bg-gray-50 group-hover:bg-white">
                  <svg className="h-6 w-6 text-gray-600 group-hover:text-rankrender-600" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 001.423 1.423l1.183.394-1.183.394a2.25 2.25 0 00-1.423 1.423z" />
                  </svg>
                </div>
                <div>
                  <Link href="/solutions/ai-search" className="font-semibold text-gray-900">
                    AI Search Optimization
                    <span className="absolute inset-0" />
                  </Link>
                  <p className="mt-1 text-gray-600">Get discovered by AI search engines and chatbots</p>
                </div>
              </div>
            </div>
          </div>
        </PopoverPanel>
      </Popover>

      <Popover className="relative">
        <PopoverButton className="flex items-center gap-x-1 text-sm font-semibold leading-6 text-slate-700 hover:text-slate-900">
          Industries
          <svg className="h-5 w-5 flex-none text-gray-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path fillRule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z" clipRule="evenodd" />
          </svg>
        </PopoverButton>
        <PopoverPanel className="absolute left-1/2 z-10 mt-5 flex w-screen max-w-max -translate-x-1/2 px-4">
          <div className="w-screen max-w-md flex-auto overflow-hidden rounded-3xl bg-white text-sm leading-6 shadow-lg ring-1 ring-gray-900/5">
            <div className="p-4">
              <div className="group relative flex gap-x-6 rounded-lg p-4 hover:bg-gray-50">
                <div className="mt-1 flex h-11 w-11 flex-none items-center justify-center rounded-lg bg-gray-50 group-hover:bg-white">
                  <span className="text-2xl">🎰</span>
                </div>
                <div>
                  <Link href="/industries/online-casinos" className="font-semibold text-gray-900">
                    Online Casinos
                    <span className="absolute inset-0" />
                  </Link>
                  <p className="mt-1 text-gray-600">SEO solutions for slot games, table games, and live dealers</p>
                </div>
              </div>
              <div className="group relative flex gap-x-6 rounded-lg p-4 hover:bg-gray-50">
                <div className="mt-1 flex h-11 w-11 flex-none items-center justify-center rounded-lg bg-gray-50 group-hover:bg-white">
                  <span className="text-2xl">⚽</span>
                </div>
                <div>
                  <Link href="/industries/sports-betting" className="font-semibold text-gray-900">
                    Sports Betting
                    <span className="absolute inset-0" />
                  </Link>
                  <p className="mt-1 text-gray-600">Optimize odds pages and live betting interfaces</p>
                </div>
              </div>
              <div className="group relative flex gap-x-6 rounded-lg p-4 hover:bg-gray-50">
                <div className="mt-1 flex h-11 w-11 flex-none items-center justify-center rounded-lg bg-gray-50 group-hover:bg-white">
                  <span className="text-2xl">🃏</span>
                </div>
                <div>
                  <Link href="/industries/poker-platforms" className="font-semibold text-gray-900">
                    Poker Platforms
                    <span className="absolute inset-0" />
                  </Link>
                  <p className="mt-1 text-gray-600">Enhance tournament and cash game visibility</p>
                </div>
              </div>
              <div className="group relative flex gap-x-6 rounded-lg p-4 hover:bg-gray-50">
                <div className="mt-1 flex h-11 w-11 flex-none items-center justify-center rounded-lg bg-gray-50 group-hover:bg-white">
                  <span className="text-2xl">🔗</span>
                </div>
                <div>
                  <Link href="/industries/affiliate-networks" className="font-semibold text-gray-900">
                    Affiliate Networks
                    <span className="absolute inset-0" />
                  </Link>
                  <p className="mt-1 text-gray-600">Maximize affiliate link performance and tracking</p>
                </div>
              </div>
              <div className="group relative flex gap-x-6 rounded-lg p-4 hover:bg-gray-50">
                <div className="mt-1 flex h-11 w-11 flex-none items-center justify-center rounded-lg bg-gray-50 group-hover:bg-white">
                  <span className="text-2xl">🛍️</span>
                </div>
                <div>
                  <Link href="/industries/ecommerce" className="font-semibold text-gray-900">
                    E-commerce
                    <span className="absolute inset-0" />
                  </Link>
                  <p className="mt-1 text-gray-600">Optimize product catalogs and shopping experiences</p>
                </div>
              </div>
              <div className="group relative flex gap-x-6 rounded-lg p-4 hover:bg-gray-50">
                <div className="mt-1 flex h-11 w-11 flex-none items-center justify-center rounded-lg bg-gray-50 group-hover:bg-white">
                  <span className="text-2xl">💻</span>
                </div>
                <div>
                  <Link href="/industries/saas" className="font-semibold text-gray-900">
                    SaaS Platforms
                    <span className="absolute inset-0" />
                  </Link>
                  <p className="mt-1 text-gray-600">Enhance software documentation and feature visibility</p>
                </div>
              </div>
            </div>
          </div>
        </PopoverPanel>
      </Popover>

      <Link href="/features" className="text-sm font-semibold leading-6 text-slate-700 hover:text-slate-900">
        Features
      </Link>

      <Link href="/pricing" className="text-sm font-semibold leading-6 text-slate-700 hover:text-slate-900">
        Pricing
      </Link>

      <Popover className="relative">
        <PopoverButton className="flex items-center gap-x-1 text-sm font-semibold leading-6 text-slate-700 hover:text-slate-900">
          Resources
          <svg className="h-5 w-5 flex-none text-gray-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path fillRule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z" clipRule="evenodd" />
          </svg>
        </PopoverButton>
        <PopoverPanel className="absolute left-1/2 z-10 mt-5 flex w-screen max-w-max -translate-x-1/2 px-4">
          <div className="w-screen max-w-md flex-auto overflow-hidden rounded-3xl bg-white text-sm leading-6 shadow-lg ring-1 ring-gray-900/5">
            <div className="p-4">
              <div className="group relative flex gap-x-6 rounded-lg p-4 hover:bg-gray-50">
                <div className="mt-1 flex h-11 w-11 flex-none items-center justify-center rounded-lg bg-gray-50 group-hover:bg-white">
                  <svg className="h-6 w-6 text-gray-600 group-hover:text-rankrender-600" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z" />
                  </svg>
                </div>
                <div>
                  <Link href="/resources/case-studies" className="font-semibold text-gray-900">
                    Case Studies
                    <span className="absolute inset-0" />
                  </Link>
                  <p className="mt-1 text-gray-600">Success stories from iGaming leaders</p>
                </div>
              </div>
              <div className="group relative flex gap-x-6 rounded-lg p-4 hover:bg-gray-50">
                <div className="mt-1 flex h-11 w-11 flex-none items-center justify-center rounded-lg bg-gray-50 group-hover:bg-white">
                  <svg className="h-6 w-6 text-gray-600 group-hover:text-rankrender-600" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M12 6.042A8.967 8.967 0 006 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 016 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 016-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0018 18a8.967 8.967 0 00-6 2.292m0-14.25v14.25" />
                  </svg>
                </div>
                <div>
                  <Link href="/resources/igaming-seo-guide" className="font-semibold text-gray-900">
                    iGaming SEO Guide
                    <span className="absolute inset-0" />
                  </Link>
                  <p className="mt-1 text-gray-600">Complete guide to iGaming SEO best practices</p>
                </div>
              </div>
              <div className="group relative flex gap-x-6 rounded-lg p-4 hover:bg-gray-50">
                <div className="mt-1 flex h-11 w-11 flex-none items-center justify-center rounded-lg bg-gray-50 group-hover:bg-white">
                  <svg className="h-6 w-6 text-gray-600 group-hover:text-rankrender-600" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.324.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 011.37.49l1.296 2.247a1.125 1.125 0 01-.26 1.431l-1.003.827c-.293.24-.438.613-.431.992a6.759 6.759 0 010 .255c-.007.378.138.75.43.99l1.005.828c.424.35.534.954.26 1.43l-1.298 2.247a1.125 1.125 0 01-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.57 6.57 0 01-.22.128c-.331.183-.581.495-.644.869l-.213 1.28c-.09.543-.56.941-1.11.941h-2.594c-.55 0-1.02-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 01-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 01-1.369-.49l-1.297-2.247a1.125 1.125 0 01.26-1.431l1.004-.827c.292-.24.437-.613.43-.992a6.932 6.932 0 010-.255c.007-.378-.138-.75-.43-.99l-1.004-.828a1.125 1.125 0 01-.26-1.43l1.297-2.247a1.125 1.125 0 011.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.087.22-.128.332-.183.582-.495.644-.869l.214-1.281z" />
                    <path strokeLinecap="round" strokeLinejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                </div>
                <div>
                  <Link href="/resources/free-tools" className="font-semibold text-gray-900">
                    Free Tools
                    <span className="absolute inset-0" />
                  </Link>
                  <p className="mt-1 text-gray-600">SEO audit tools and performance analyzers</p>
                </div>
              </div>
              <div className="group relative flex gap-x-6 rounded-lg p-4 hover:bg-gray-50">
                <div className="mt-1 flex h-11 w-11 flex-none items-center justify-center rounded-lg bg-gray-50 group-hover:bg-white">
                  <svg className="h-6 w-6 text-gray-600 group-hover:text-rankrender-600" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M12 7.5h1.5m-1.5 3h1.5m-7.5 3h7.5m-7.5 3h7.5m3-9h3.375c.621 0 1.125.504 1.125 1.125V18a2.25 2.25 0 01-2.25 2.25M16.5 7.5V18a2.25 2.25 0 002.25 2.25M16.5 7.5V4.875c0-.621-.504-1.125-1.125-1.125H4.125C3.504 3.75 3 4.254 3 4.875V18a2.25 2.25 0 002.25 2.25h13.5M6 7.5h3v3H6v-3z" />
                  </svg>
                </div>
                <div>
                  <Link href="/resources/blog" className="font-semibold text-gray-900">
                    Blog
                    <span className="absolute inset-0" />
                  </Link>
                  <p className="mt-1 text-gray-600">Latest insights on iGaming SEO and trends</p>
                </div>
              </div>
            </div>
          </div>
        </PopoverPanel>
      </Popover>
    </nav>
  )
}

function MobileNavigation() {
  return (
    <Popover>
      <PopoverButton className="relative z-50 -m-2 inline-flex items-center rounded-lg stroke-gray-900 p-2 hover:bg-gray-200/50 hover:stroke-gray-600 active:stroke-gray-900 ui-not-focus-visible:outline-none">
        {({ open }) => (
          <>
            <span className="sr-only">Toggle site navigation</span>
            <MobileNavIcon open={open} />
          </>
        )}
      </PopoverButton>
      <PopoverBackdrop className="fixed top-20 left-0 right-0 bottom-0 z-30 bg-black/50 transition-opacity duration-500 ease-out data-[closed]:opacity-0 data-[open]:opacity-100" />
      <PopoverPanel className="fixed top-20 bottom-0 right-0 z-40 w-full max-w-sm bg-white shadow-2xl transition-all duration-400 ease-[cubic-bezier(0.25,0.46,0.45,0.94)] data-[closed]:translate-x-full data-[open]:translate-x-0">
        <div className="flex h-full flex-col">

          {/* Navigation Content */}
          <div className="flex-1 overflow-y-auto px-6 py-6">
            <div className="space-y-6">
              <div className="animate-slide-in-stagger-1">
                <div className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-3">Solutions</div>
                <div className="space-y-1">
                  <MobileNavLink href="/solutions/ai-search">AI Search Optimization</MobileNavLink>
                  <MobileNavLink href="/solutions/ecommerce">E-commerce SEO</MobileNavLink>
                  <MobileNavLink href="/solutions/javascript-seo">JavaScript SEO</MobileNavLink>
                </div>
              </div>

              <div className="animate-slide-in-stagger-2">
                <div className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-3">Industries</div>
                <div className="space-y-1">
                  <MobileNavLink href="/industries/online-casinos">Online Casinos</MobileNavLink>
                  <MobileNavLink href="/industries/sports-betting">Sports Betting</MobileNavLink>
                  <MobileNavLink href="/industries/poker-platforms">Poker Platforms</MobileNavLink>
                  <MobileNavLink href="/industries/ecommerce">E-commerce</MobileNavLink>
                </div>
              </div>

              <div className="animate-slide-in-stagger-3">
                <div className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-3">Product</div>
                <div className="space-y-1">
                  <MobileNavLink href="/features">Features</MobileNavLink>
                  <MobileNavLink href="/pricing">Pricing</MobileNavLink>
                  <MobileNavLink href="/resources/case-studies">Case Studies</MobileNavLink>
                </div>
              </div>

              <div className="animate-slide-in-stagger-4">
                <div className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-3">Resources</div>
                <div className="space-y-1">
                  <MobileNavLink href="/resources/blog">Blog</MobileNavLink>
                  <MobileNavLink href="/resources/igaming-seo-guide">iGaming SEO Guide</MobileNavLink>
                  <MobileNavLink href="/resources/free-tools">Free Tools</MobileNavLink>
                </div>
              </div>
            </div>
          </div>

          {/* Footer Actions */}
          <div className="border-t border-gray-200 p-6 animate-slide-in-stagger-5">
            <div className="space-y-3">
              <Button href="/contact" variant="outline" className="w-full justify-center">Contact Us</Button>
              <Button href="/register" className="w-full justify-center">Get Started</Button>
            </div>
          </div>
        </div>
      </PopoverPanel>
    </Popover>
  )
}

export function Header() {
  return (
    <header className="py-10">
      <Container>
        <nav className="relative z-50 flex justify-between">
          <div className="flex items-center md:gap-x-12">
            <Link href="/" aria-label="Home">
              <LogoRankRender className="h-10 w-auto" />
            </Link>
            <DesktopNavigation />
          </div>
          <div className="flex items-center gap-x-5 md:gap-x-8">
            <div className="hidden md:block">
              <Link href="/contact" className="text-sm font-semibold leading-6 text-slate-700 hover:text-slate-900">
                Contact
              </Link>
            </div>
            <Button href="/register" color="rankrender">
              <span>
                Get started <span className="hidden lg:inline">today</span>
              </span>
            </Button>
            <div className="-mr-1 md:hidden">
              <MobileNavigation />
            </div>
          </div>
        </nav>
      </Container>
    </header>
  )
}
