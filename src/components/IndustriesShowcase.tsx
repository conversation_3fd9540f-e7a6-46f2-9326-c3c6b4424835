import { Container } from '@/components/Container'
import { Button } from '@/components/Button'

const industries = [
  {
    name: 'E-commerce',
    description: 'Optimize product catalogs, inventory updates, and dynamic pricing for maximum search visibility and conversions.',
    icon: '🛒',
    features: ['Product page optimization', 'Inventory tracking', 'Price monitoring', 'Review integration'],
    highlighted: true,
  },
  {
    name: 'SaaS Platforms',
    description: 'Optimize feature pages, documentation, and user-generated content for better search visibility and user acquisition.',
    icon: '💻',
    features: ['Feature page optimization', 'Documentation indexing', 'User content optimization', 'API documentation'],
    highlighted: false,
  },
  {
    name: 'Marketplaces',
    description: 'Optimize seller listings, product catalogs, and user-generated content for maximum marketplace visibility.',
    icon: '🏢',
    features: ['Listing optimization', 'Seller page indexing', 'Review optimization', 'Category management'],
    highlighted: false,
  },
  {
    name: 'Content & Media',
    description: 'Optimize news articles, blog posts, videos, and multimedia content for search engines and social media sharing.',
    icon: '📰',
    features: ['Article optimization', 'Video content indexing', 'Social media previews', 'Breaking news tracking'],
    highlighted: false,
  },
  {
    name: 'Financial Services',
    description: 'Ensure your financial products, market data, and educational content are discoverable while maintaining compliance.',
    icon: '💰',
    features: ['Product page optimization', 'Market data indexing', 'Compliance monitoring', 'Educational content'],
    highlighted: false,
  },
  {
    name: 'Travel & Hospitality',
    description: 'Optimize booking pages, destination content, and real-time availability for better search visibility.',
    icon: '✈️',
    features: ['Booking optimization', 'Destination content', 'Availability tracking', 'Review integration'],
    highlighted: false,
  },
  {
    name: 'Gaming & Entertainment',
    description: 'Specialized optimization for gaming platforms and entertainment content with compliance considerations.',
    icon: '🎲',
    features: ['Content optimization', 'Compliance monitoring', 'Real-time updates', 'User engagement'],
    highlighted: false,
  },
]

export function IndustriesShowcase() {
  return (
    <div className="bg-gray-50 py-24 sm:py-32">
      <Container>
        <div className="mx-auto max-w-2xl text-center">
          <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
            Built for every industry
          </h2>
          <p className="mt-6 text-lg leading-8 text-gray-600">
            While we specialize in iGaming, RankRender works for any JavaScript-heavy platform that needs better SEO performance.
          </p>
        </div>

        <div className="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-8 sm:mt-20 lg:mx-0 lg:max-w-none lg:grid-cols-3">
          {industries.map((industry) => (
            <div
              key={industry.name}
              className={`relative rounded-2xl p-8 ${
                industry.highlighted
                  ? 'bg-rankrender-600 text-white ring-2 ring-rankrender-600'
                  : 'bg-white text-gray-900 ring-1 ring-gray-200'
              }`}
            >
              {industry.highlighted && (
                <div className="absolute -top-4 left-1/2 -translate-x-1/2">
                  <span className="inline-flex items-center rounded-full bg-rankrender-100 px-3 py-1 text-sm font-medium text-rankrender-800">
                    Our Specialty
                  </span>
                </div>
              )}

              <div className="flex items-center gap-x-3">
                <span className="text-3xl">{industry.icon}</span>
                <h3 className={`text-lg font-semibold leading-8 ${industry.highlighted ? 'text-white' : 'text-gray-900'}`}>
                  {industry.name}
                </h3>
              </div>

              <p className={`mt-4 text-base leading-7 ${industry.highlighted ? 'text-rankrender-100' : 'text-gray-600'}`}>
                {industry.description}
              </p>

              <ul className={`mt-6 space-y-2 text-sm ${industry.highlighted ? 'text-rankrender-100' : 'text-gray-600'}`}>
                {industry.features.map((feature, index) => (
                  <li key={index} className="flex items-center">
                    <svg
                      className={`mr-2 h-4 w-4 ${industry.highlighted ? 'text-rankrender-200' : 'text-green-500'}`}
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path fillRule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clipRule="evenodd" />
                    </svg>
                    {feature}
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        <div className="mx-auto mt-16 max-w-2xl text-center">
          <h3 className="text-xl font-semibold text-gray-900">
            Don't see your industry?
          </h3>
          <p className="mt-4 text-base text-gray-600">
            RankRender works with any JavaScript-heavy platform. Contact us to discuss your specific SEO challenges.
          </p>
          <div className="mt-8 flex flex-col sm:flex-row justify-center gap-4">
            <Button href="/register" className="text-lg px-8 py-4">
              Start Free Trial
            </Button>
            <Button href="/contact" variant="outline" className="text-lg px-8 py-4">
              Discuss Your Needs
            </Button>
          </div>
        </div>
      </Container>
    </div>
  )
}
