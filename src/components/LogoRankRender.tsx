export function LogoRankRender(props: React.ComponentPropsWithoutRef<'svg'>) {
  return (
    <svg aria-hidden="true" viewBox="0 0 443 90" {...props}>
        <g fill="#000000" transform="matrix(5.714285714285714,0,0,5.714285714285714,105.7142903464181,-26.371425356183735)">
            <path d="M2.21 17.60L0.75 17.60L0.75 10.38L2.21 10.38L2.21 11.38Q2.24 11.34 2.34 11.20L2.34 11.20L2.50 10.95Q3.01 10.26 4.17 10.26L4.17 10.26Q4.29 10.26 4.42 10.28L4.42 10.28L4.42 11.50Q3.17 11.53 2.63 12.20L2.63 12.20Q2.38 12.50 2.21 12.97L2.21 12.97L2.21 17.60ZM6.31 15.74L6.31 15.74Q6.31 16.14 6.51 16.32Q6.71 16.50 7 16.50Q7.29 16.50 7.44 16.47Q7.59 16.43 7.73 16.37L7.73 16.37L7.98 16.24Q8.09 16.17 8.23 16.06L8.23 16.06L8.43 15.91Q8.48 15.86 8.61 15.75L8.61 15.75L8.73 15.63L8.73 13.95Q7.20 14.30 6.73 14.71L6.73 14.71Q6.31 15.08 6.31 15.74ZM10.40 17.60L8.93 17.60Q8.73 17.47 8.73 16.96L8.73 16.96L8.60 17.11Q8.01 17.74 6.92 17.74L6.92 17.74Q5.59 17.74 5.10 16.80L5.10 16.80Q4.85 16.34 4.85 15.73Q4.85 15.12 5.06 14.65Q5.28 14.17 5.76 13.81L5.76 13.81Q6.64 13.15 8.73 12.74L8.73 12.74Q8.73 12.25 8.70 12.14Q8.67 12.03 8.63 11.94Q8.59 11.84 8.54 11.79Q8.48 11.74 8.40 11.68Q8.32 11.61 8.21 11.59L8.21 11.59Q7.94 11.52 7.62 11.52Q7.29 11.52 7.01 11.62Q6.73 11.72 6.56 11.84Q6.39 11.96 6.25 12.13L6.25 12.13Q6.08 12.35 6.00 12.51L6.00 12.51L5.04 11.92Q5.35 11.30 5.56 11.09Q5.78 10.88 6.00 10.73Q6.21 10.58 6.45 10.49L6.45 10.49Q7.02 10.28 7.62 10.28Q8.21 10.28 8.56 10.35Q8.91 10.41 9.23 10.58Q9.55 10.75 9.75 11.03L9.75 11.03Q10.19 11.63 10.19 12.80L10.19 12.80L10.19 16.58Q10.19 16.77 10.26 17.12Q10.34 17.47 10.40 17.60L10.40 17.60ZM16.88 12.19L16.88 12.19L16.88 17.60L15.42 17.60L15.42 12.57Q15.42 12.08 15.12 11.79Q14.83 11.50 14.42 11.50Q14.01 11.50 13.60 11.74Q13.20 11.98 13.00 12.22L13.00 12.22L13.00 17.60L11.54 17.60L11.54 10.38L13.00 10.38L13.00 11.15Q13.40 10.75 13.83 10.51Q14.27 10.26 14.85 10.26L14.85 10.26Q15.74 10.26 16.31 10.74Q16.88 11.22 16.88 12.19ZM19.85 17.60L18.39 17.60L18.39 7.24L19.85 7.24L19.85 13.07L21.93 10.38L23.71 10.38L21.62 13.05L23.83 17.60L22.18 17.60L20.60 14.34L19.85 15.29L19.85 17.60ZM26.08 17.60L24.62 17.60L24.62 10.38L26.08 10.38L26.08 11.38Q26.11 11.34 26.20 11.20L26.20 11.20L26.37 10.95Q26.87 10.26 28.03 10.26L28.03 10.26Q28.16 10.26 28.28 10.28L28.28 10.28L28.28 11.50Q27.04 11.53 26.49 12.20L26.49 12.20Q26.24 12.50 26.08 12.97L26.08 12.97L26.08 17.60ZM30.06 13.14L30.06 13.14L32.59 13.14Q32.59 12.61 32.40 12.22L32.40 12.22Q32.05 11.50 31.42 11.50L31.42 11.50Q31.04 11.50 30.76 11.67Q30.47 11.83 30.33 12.10L30.33 12.10Q30.06 12.60 30.06 13.14ZM28.91 16.20Q28.70 15.73 28.61 15.23Q28.53 14.74 28.53 13.98Q28.53 13.23 28.70 12.52Q28.88 11.81 29.16 11.39Q29.44 10.96 29.84 10.71L29.84 10.71Q30.52 10.26 31.43 10.26L31.43 10.26Q32.29 10.26 32.94 10.77L32.94 10.77Q34.14 11.74 34.08 14.38L34.08 14.38L29.99 14.38Q29.99 15.76 30.72 16.24L30.72 16.24Q31.10 16.48 31.51 16.48Q31.92 16.48 32.20 16.38Q32.47 16.28 32.63 16.15Q32.80 16.02 32.94 15.85L32.94 15.85Q33.12 15.61 33.19 15.44L33.19 15.44L34.12 16.04Q33.61 16.92 33.04 17.29L33.04 17.29Q32.40 17.72 31.64 17.72Q30.88 17.72 30.34 17.52Q29.80 17.32 29.47 16.99Q29.13 16.67 28.91 16.20ZM40.62 12.19L40.62 12.19L40.62 17.60L39.16 17.60L39.16 12.57Q39.16 12.08 38.86 11.79Q38.56 11.50 38.15 11.50Q37.74 11.50 37.34 11.74Q36.93 11.98 36.74 12.22L36.74 12.22L36.74 17.60L35.27 17.60L35.27 10.38L36.74 10.38L36.74 11.15Q37.13 10.75 37.57 10.51Q38.00 10.26 38.58 10.26L38.58 10.26Q39.47 10.26 40.04 10.74Q40.62 11.22 40.62 12.19ZM46.14 15.65L46.14 12.20Q45.46 11.50 44.66 11.50L44.66 11.50Q43.83 11.50 43.46 12.22L43.46 12.22Q43.14 12.84 43.14 14.10L43.14 14.10Q43.14 15.49 43.59 16.02L43.59 16.02Q43.98 16.48 44.73 16.48L44.73 16.48Q45.52 16.48 46.04 15.78L46.04 15.78Q46.13 15.65 46.14 15.65L46.14 15.65ZM47.60 7.24L47.60 16.62Q47.60 17.10 47.79 17.60L47.79 17.60L46.25 17.60Q46.22 17.56 46.20 17.50L46.20 17.50L46.14 17.06Q45.62 17.72 44.62 17.72L44.62 17.72Q42.90 17.72 42.12 16.13L42.12 16.13Q41.68 15.24 41.68 14.12L41.68 14.12Q41.68 11.52 42.96 10.70L42.96 10.70Q43.65 10.26 44.71 10.26L44.71 10.26Q45.24 10.26 45.63 10.47Q46.01 10.68 46.14 10.86L46.14 10.86L46.14 7.24L47.60 7.24ZM50.00 13.14L50.00 13.14L52.53 13.14Q52.53 12.61 52.34 12.22L52.34 12.22Q51.99 11.50 51.36 11.50L51.36 11.50Q50.98 11.50 50.70 11.67Q50.42 11.83 50.27 12.10L50.27 12.10Q50.00 12.60 50.00 13.14ZM48.85 16.20Q48.64 15.73 48.55 15.23Q48.47 14.74 48.47 13.98Q48.47 13.23 48.64 12.52Q48.82 11.81 49.10 11.39Q49.38 10.96 49.78 10.71L49.78 10.71Q50.46 10.26 51.37 10.26L51.37 10.26Q52.23 10.26 52.88 10.77L52.88 10.77Q54.08 11.74 54.02 14.38L54.02 14.38L49.93 14.38Q49.93 15.76 50.66 16.24L50.66 16.24Q51.04 16.48 51.45 16.48Q51.86 16.48 52.14 16.38Q52.41 16.28 52.58 16.15Q52.74 16.02 52.88 15.85L52.88 15.85Q53.06 15.61 53.13 15.44L53.13 15.44L54.06 16.04Q53.55 16.92 52.98 17.29L52.98 17.29Q52.34 17.72 51.58 17.72Q50.82 17.72 50.28 17.52Q49.75 17.32 49.41 16.99Q49.07 16.67 48.85 16.20ZM56.68 17.60L55.21 17.60L55.21 10.38L56.68 10.38L56.68 11.38Q56.70 11.34 56.80 11.20L56.80 11.20L56.96 10.95Q57.47 10.26 58.63 10.26L58.63 10.26Q58.75 10.26 58.88 10.28L58.88 10.28L58.88 11.50Q57.63 11.53 57.09 12.20L57.09 12.20Q56.84 12.50 56.68 12.97L56.68 12.97L56.68 17.60Z"></path>
        </g>
        <defs>
            <linearGradient gradientTransform="rotate(25)" id="rankrender-gradient" x1="0%" y1="0%" x2="100%" y2="0%">
                <stop offset="0%" style={{stopColor: 'rgb(65, 226, 150)', stopOpacity: 1}}></stop>
                <stop offset="100%" style={{stopColor: 'rgb(0, 196, 238)', stopOpacity: 1}}></stop>
            </linearGradient>
        </defs>
        <g transform="matrix(2.8125,0,0,2.8125,0,1.0290013551712036)" stroke="none" fill="url(#rankrender-gradient)">
            <path d="M0 31.984h32v-32H0v32zm2-30h28v28H2v-28z"></path>
            <path d="M6.439 16.831h8.959l-4.48-7.741zM16.602 13.331h8.959L21.082 5.59zM6.439 26.41h8.959l-4.48-7.741zM16.602 22.91h8.959l-4.479-7.741z"></path>
        </g>
    </svg>
  )
}
