'use client'

import { useEffect, useState } from 'react'
import Image from 'next/image'
import { Tab, TabGroup, TabList, TabPanel, TabPanels } from '@headlessui/react'
import clsx from 'clsx'

import { Container } from '@/components/Container'
import backgroundImage from '@/images/background_rankrender_gradiant_180.png'
import screenshotExpenses from '@/images/screenshots/expenses.png'
import screenshotReporting from '@/images/screenshots/reporting.png'
import screenshotVatReturns from '@/images/screenshots/vat-returns.png'

const features = [
  {
    title: 'Dynamic Content Optimization',
    description:
      'Automatically optimize your entire product catalog, user-generated content, and interactive features for search engines. Get new content indexed within hours, not weeks.',
    image: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-4.0.3&auto=format&fit=crop&w=922&h=621&q=80',
    icon: '🚀',
  },
  {
    title: 'Real-Time SEO Updates',
    description:
      'Real-time optimization for pricing pages, inventory updates, and live content. Ensure your platform ranks for every important update and change.',
    image: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&auto=format&fit=crop&w=922&h=621&q=80',
    icon: '⚡',
  },
  {
    title: 'Social Media Integration',
    description:
      'Perfect link previews on Facebook, Twitter, LinkedIn, and WhatsApp. Ensure your content looks great when shared across all social platforms.',
    image: 'https://images.unsplash.com/photo-1611224923853-80b023f02d71?ixlib=rb-4.0.3&auto=format&fit=crop&w=922&h=621&q=80',
    icon: '🔗',
  },
  {
    title: 'AI Search Visibility',
    description:
      'Get discovered by ChatGPT, Claude, Perplexity, and other AI search engines. Future-proof your SEO strategy for the age of AI-powered search.',
    image: 'https://images.unsplash.com/photo-1677442136019-21780ecad995?ixlib=rb-4.0.3&auto=format&fit=crop&w=922&h=621&q=80',
    icon: '🤖',
  },
]

export function PrimaryFeatures() {
  let [tabOrientation, setTabOrientation] = useState<'horizontal' | 'vertical'>(
    'horizontal',
  )

  useEffect(() => {
    let lgMediaQuery = window.matchMedia('(min-width: 1024px)')

    function onMediaQueryChange({ matches }: { matches: boolean }) {
      setTabOrientation(matches ? 'vertical' : 'horizontal')
    }

    onMediaQueryChange(lgMediaQuery)
    lgMediaQuery.addEventListener('change', onMediaQueryChange)

    return () => {
      lgMediaQuery.removeEventListener('change', onMediaQueryChange)
    }
  }, [])

  return (
    <section
      id="features"
      aria-label="Features for running your books"
      className="relative overflow-hidden bg-rankrender-600 pt-20 pb-28 sm:py-32"
    >
      <Image
        className="absolute top-1/2 left-1/2 max-w-none translate-x-[-44%] translate-y-[-42%]"
        src={backgroundImage}
        alt=""
        width={2245}
        height={1636}
        unoptimized
      />
      <Container className="relative">
        <div className="max-w-2xl md:mx-auto md:text-center xl:max-w-none">
          <h2 className="font-display text-3xl tracking-tight text-white sm:text-4xl md:text-5xl">
            Built for modern JavaScript platforms
          </h2>
          <p className="mt-6 text-lg tracking-tight text-rankrender-100">
            From e-commerce to SaaS applications, we understand the unique SEO challenges of dynamic content. Get your platform discovered faster with specialized optimization.
          </p>
        </div>
        {/*<TabGroup*/}
        {/*  className="mt-16 grid grid-cols-1 items-center gap-y-2 pt-10 sm:gap-y-6 md:mt-20 lg:grid-cols-12 lg:pt-0"*/}
        {/*  vertical={tabOrientation === 'vertical'}*/}
        {/*>*/}
        {/*  {({ selectedIndex }) => (*/}
        {/*    <>*/}
        {/*      <div className="-mx-4 flex overflow-x-auto pb-4 sm:mx-0 sm:overflow-visible sm:pb-0 lg:col-span-5">*/}
        {/*        <TabList className="relative z-10 flex gap-x-4 px-4 whitespace-nowrap sm:mx-auto sm:px-0 lg:mx-0 lg:block lg:gap-x-0 lg:gap-y-1 lg:whitespace-normal">*/}
        {/*          {features.map((feature, featureIndex) => (*/}
        {/*            <div*/}
        {/*              key={feature.title}*/}
        {/*              className={clsx(*/}
        {/*                'group relative rounded-full px-4 py-1 lg:rounded-l-xl lg:rounded-r-none lg:p-6',*/}
        {/*                selectedIndex === featureIndex*/}
        {/*                  ? 'bg-white lg:bg-white/10 lg:ring-1 lg:ring-white/10 lg:ring-inset'*/}
        {/*                  : 'hover:bg-white/10 lg:hover:bg-white/5',*/}
        {/*              )}*/}
        {/*            >*/}
        {/*              <h3>*/}
        {/*                <Tab*/}
        {/*                  className={clsx(*/}
        {/*                    'font-display text-lg data-selected:not-data-focus:outline-hidden',*/}
        {/*                    selectedIndex === featureIndex*/}
        {/*                      ? 'text-rankrender-600 lg:text-white'*/}
        {/*                      : 'text-rankrender-100 hover:text-white lg:text-white',*/}
        {/*                  )}*/}
        {/*                >*/}
        {/*                  <span className="absolute inset-0 rounded-full lg:rounded-l-xl lg:rounded-r-none" />*/}
        {/*                  {feature.title}*/}
        {/*                </Tab>*/}
        {/*              </h3>*/}
        {/*              <p*/}
        {/*                className={clsx(*/}
        {/*                  'mt-2 hidden text-sm lg:block',*/}
        {/*                  selectedIndex === featureIndex*/}
        {/*                    ? 'text-white'*/}
        {/*                    : 'text-rankrender-100 group-hover:text-white',*/}
        {/*                )}*/}
        {/*              >*/}
        {/*                {feature.description}*/}
        {/*              </p>*/}
        {/*            </div>*/}
        {/*          ))}*/}
        {/*        </TabList>*/}
        {/*      </div>*/}
        {/*      <TabPanels className="lg:col-span-7">*/}
        {/*        {features.map((feature) => (*/}
        {/*          <TabPanel key={feature.title} unmount={false}>*/}
        {/*            <div className="relative sm:px-6 lg:hidden">*/}
        {/*              <div className="absolute -inset-x-4 top-[-6.5rem] bottom-[-4.25rem] bg-white/10 ring-1 ring-white/10 ring-inset sm:inset-x-0 sm:rounded-t-xl" />*/}
        {/*              <p className="relative mx-auto max-w-2xl text-base text-white sm:text-center">*/}
        {/*                {feature.description}*/}
        {/*              </p>*/}
        {/*            </div>*/}
        {/*            <div className="mt-10 w-[45rem] overflow-hidden rounded-xl bg-slate-50 shadow-xl shadow-rankrender-900/20 sm:w-auto lg:mt-0 lg:w-[67.8125rem]">*/}
        {/*              <Image*/}
        {/*                className="w-full"*/}
        {/*                src={feature.image}*/}
        {/*                alt=""*/}
        {/*                priority*/}
        {/*                width={922}*/}
        {/*                height={621}*/}
        {/*                sizes="(min-width: 1024px) 67.8125rem, (min-width: 640px) 100vw, 45rem"*/}
        {/*              />*/}
        {/*            </div>*/}
        {/*          </TabPanel>*/}
        {/*        ))}*/}
        {/*      </TabPanels>*/}
        {/*    </>*/}
        {/*  )}*/}
        {/*</TabGroup>*/}
      </Container>
    </section>
  )
}
