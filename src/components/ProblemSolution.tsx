import { Container } from '@/components/Container'
import { Button } from '@/components/Button'

const problems = [
  {
    title: 'Search engines can\'t crawl your games',
    description: 'Your slot games, table games, and live dealer content are invisible to Google because they\'re built with JavaScript.',
    icon: '🎰',
  },
  {
    title: 'Odds pages never get indexed',
    description: 'Sports betting odds change constantly, making it impossible for search engines to properly index your sportsbook.',
    icon: '⚽',
  },
  {
    title: 'Promotions disappear from search',
    description: 'Your casino bonuses and tournament announcements get buried because search engines can\'t keep up with updates.',
    icon: '🎁',
  },
  {
    title: 'Compliance kills your SEO',
    description: 'Gambling regulations make it challenging to optimize content without risking compliance violations.',
    icon: '⚖️',
  },
]

const solutions = [
  {
    title: 'Instant Game Discovery',
    description: 'Every slot game, table game, and live dealer option gets optimized and indexed within hours, not weeks.',
    icon: '🚀',
  },
  {
    title: 'Real-Time Odds Optimization',
    description: 'Sports betting odds and live events are automatically optimized as they change, ensuring maximum visibility.',
    icon: '⚡',
  },
  {
    title: 'Smart Promotion Tracking',
    description: 'Casino bonuses and tournaments are tracked and optimized in real-time, keeping your offers visible.',
    icon: '🎯',
  },
  {
    title: 'Compliance-First SEO',
    description: 'Built-in compliance monitoring ensures your SEO optimizations meet gambling regulations across jurisdictions.',
    icon: '🛡️',
  },
]

export function ProblemSolution() {
  return (
    <div className="bg-white py-24 sm:py-32">
      <Container>
        <div className="mx-auto max-w-2xl text-center">
          <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
            The iGaming SEO challenge
          </h2>
          <p className="mt-6 text-lg leading-8 text-gray-600">
            Traditional SEO tools weren't built for the unique challenges of casino, sportsbook, and poker platforms.
          </p>
        </div>

        {/* Problems */}
        <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
          <div className="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-2">
            {problems.map((problem, index) => (
              <div key={index} className="flex flex-col items-start">
                <div className="flex items-center gap-x-3">
                  <span className="text-2xl">{problem.icon}</span>
                  <h3 className="text-lg font-semibold leading-8 tracking-tight text-red-600">
                    {problem.title}
                  </h3>
                </div>
                <p className="mt-4 text-base leading-7 text-gray-600">
                  {problem.description}
                </p>
              </div>
            ))}
          </div>
        </div>

        {/* Divider */}
        <div className="mx-auto mt-24 max-w-2xl text-center">
          <div className="relative">
            <div className="absolute inset-0 flex items-center" aria-hidden="true">
              <div className="w-full border-t border-gray-300" />
            </div>
            <div className="relative flex justify-center">
              <span className="bg-white px-6 text-lg font-semibold text-rankrender-600">
                RankRender solves this
              </span>
            </div>
          </div>
        </div>

        {/* Solutions */}
        <div className="mx-auto mt-24 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
          <div className="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-2">
            {solutions.map((solution, index) => (
              <div key={index} className="flex flex-col items-start">
                <div className="flex items-center gap-x-3">
                  <span className="text-2xl">{solution.icon}</span>
                  <h3 className="text-lg font-semibold leading-8 tracking-tight text-green-600">
                    {solution.title}
                  </h3>
                </div>
                <p className="mt-4 text-base leading-7 text-gray-600">
                  {solution.description}
                </p>
              </div>
            ))}
          </div>
        </div>

        {/* CTA */}
        <div className="mx-auto mt-16 max-w-2xl text-center">
          <h3 className="text-2xl font-bold tracking-tight text-gray-900">
            Ready to solve your iGaming SEO challenges?
          </h3>
          <p className="mt-4 text-lg leading-8 text-gray-600">
            Start your free trial today and see the difference specialized iGaming SEO can make.
          </p>
          <div className="mt-8 flex flex-col sm:flex-row justify-center gap-4">
            <Button href="/register" className="text-lg px-8 py-4">
              Start Free Trial
            </Button>
            <Button href="/contact" variant="outline" className="text-lg px-8 py-4">
              Talk to Expert
            </Button>
          </div>
        </div>
      </Container>
    </div>
  )
}
