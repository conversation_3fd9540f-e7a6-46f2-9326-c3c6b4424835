import Image from 'next/image'

import backgroundImage from '@/images/rankrender_bg_2.png'
import logoWhite from '@/images/logos/rankrender/default-monochrome-white.svg'

export function SlimLayout({ children }: { children: React.ReactNode }) {
  return (
    <>
      <div className="relative flex min-h-full shrink-0 justify-center md:px-12 lg:px-0">
        <div className="relative z-10 flex flex-1 flex-col bg-white px-4 py-10 shadow-2xl sm:justify-center md:flex-none md:px-28">
          <main className="mx-auto w-full max-w-md sm:px-4 md:w-96 md:max-w-sm md:px-0">
            {children}
          </main>
        </div>
        <div className="hidden sm:contents lg:relative lg:block lg:flex-1">
          <div className="absolute inset-0 bg-gradient-to-br from-rankrender-600 via-rankrender-700 to-rankrender-800">
            <Image
              className="absolute inset-0 h-full w-full object-cover opacity-20"
              src={backgroundImage}
              alt=""
              unoptimized
            />
          </div>
          <div className="relative z-10 flex h-full flex-col justify-center px-12 py-16">
            <div className="max-w-md">
              <Image
                src={logoWhite}
                alt="RankRender"
                className="h-12 w-auto mb-12"
                unoptimized
              />

              <h2 className="text-3xl font-bold text-white mb-6">
                Join 1,000+ Companies Boosting Their SEO
              </h2>

              <div className="space-y-6 text-white/90">
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 w-6 h-6 bg-white/20 rounded-full flex items-center justify-center mt-0.5">
                    <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="font-semibold text-white mb-1">Free Forever Plan</h3>
                    <p className="text-sm text-white/80">Start with 1,000 renders per month. No credit card required.</p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 w-6 h-6 bg-white/20 rounded-full flex items-center justify-center mt-0.5">
                    <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="font-semibold text-white mb-1">Instant JavaScript SEO</h3>
                    <p className="text-sm text-white/80">Get your dynamic content indexed by search engines in minutes.</p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 w-6 h-6 bg-white/20 rounded-full flex items-center justify-center mt-0.5">
                    <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="font-semibold text-white mb-1">AI Search Ready</h3>
                    <p className="text-sm text-white/80">Optimize for ChatGPT, Perplexity, and other AI search engines.</p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 w-6 h-6 bg-white/20 rounded-full flex items-center justify-center mt-0.5">
                    <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="font-semibold text-white mb-1">Setup in 5 Minutes</h3>
                    <p className="text-sm text-white/80">Add your domain and start optimizing immediately.</p>
                  </div>
                </div>
              </div>

              <div className="mt-12 p-6 bg-white/10 backdrop-blur-sm rounded-lg border border-white/20">
                <div className="flex items-center space-x-4 mb-4">
                  <div className="flex -space-x-2">
                    <div className="w-8 h-8 bg-white/20 rounded-full border-2 border-white/30"></div>
                    <div className="w-8 h-8 bg-white/20 rounded-full border-2 border-white/30"></div>
                    <div className="w-8 h-8 bg-white/20 rounded-full border-2 border-white/30"></div>
                    <div className="w-8 h-8 bg-white/20 rounded-full border-2 border-white/30 flex items-center justify-center">
                      <span className="text-xs text-white font-medium">+1K</span>
                    </div>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-white">Trusted by 1,000+ companies</p>
                    <p className="text-xs text-white/70">From startups to Fortune 500</p>
                  </div>
                </div>
                <div className="grid grid-cols-3 gap-4 text-center">
                  <div>
                    <div className="text-lg font-bold text-white">520%</div>
                    <div className="text-xs text-white/70">Avg Traffic Increase</div>
                  </div>
                  <div>
                    <div className="text-lg font-bold text-white">24h</div>
                    <div className="text-xs text-white/70">Indexing Speed</div>
                  </div>
                  <div>
                    <div className="text-lg font-bold text-white">99.9%</div>
                    <div className="text-xs text-white/70">Uptime SLA</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}
