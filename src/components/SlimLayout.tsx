import Image from 'next/image'

import backgroundImage from '@/images/rankrender_bg_2.png'
import logoWhite from '@/images/logos/rankrender/isolated-monochrome-white.svg'

export function SlimLayout({ children }: { children: React.ReactNode }) {
  return (
    <>
      <div className="relative flex min-h-full shrink-0 justify-center md:px-12 lg:px-0">
        <div className="relative z-10 flex flex-1 flex-col bg-white px-4 py-10 shadow-2xl sm:justify-center md:flex-none md:px-28">
          <main className="mx-auto w-full max-w-md sm:px-4 md:w-96 md:max-w-sm md:px-0">
            {children}
          </main>
        </div>
        <div className="hidden sm:contents lg:relative lg:block lg:flex-1">
          <div className="absolute inset-0 bg-gradient-to-br from-rankrender-600 via-rankrender-700 to-rankrender-800">
            <Image
              className="absolute inset-0 h-full w-full object-cover opacity-20"
              src={backgroundImage}
              alt=""
              unoptimized
            />
          </div>
          <div className="relative z-10 flex h-full flex-col justify-center items-center px-12 py-16">
            <div className="text-center max-w-sm">
              <Image
                src={logoWhite}
                alt="RankRender"
                className="h-16 w-auto mx-auto mb-8"
                unoptimized
              />

              <h2 className="text-2xl font-bold text-white mb-6">
                Boost Your SEO in Minutes
              </h2>

              <div className="space-y-4 text-white/90 mb-8">
                <div className="flex items-center justify-center space-x-2">
                  <svg className="w-4 h-4 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <span className="text-sm">Free forever plan</span>
                </div>
                <div className="flex items-center justify-center space-x-2">
                  <svg className="w-4 h-4 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <span className="text-sm">Setup in 5 minutes</span>
                </div>
                <div className="flex items-center justify-center space-x-2">
                  <svg className="w-4 h-4 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <span className="text-sm">No credit card required</span>
                </div>
              </div>

              <div className="p-4 bg-white/10 backdrop-blur-sm rounded-lg border border-white/20">
                <p className="text-sm font-medium text-white mb-2">Trusted by 1,000+ companies</p>
                <div className="flex justify-center space-x-1 mb-2">
                  <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
                  <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
                  <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
                  <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
                  <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
                </div>
                <p className="text-xs text-white/70">5.0 stars • 99.9% uptime</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}
