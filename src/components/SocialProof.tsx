import { Container } from '@/components/Container'

const stats = [
  { id: 1, name: 'iGaming operators trust RankRender', value: '50+' },
  { id: 2, name: 'Average increase in organic traffic', value: '250%' },
  { id: 3, name: 'Faster content indexing', value: '85%' },
  { id: 4, name: 'Uptime guarantee', value: '99.9%' },
]

const testimonials = [
  {
    body: 'RankRender transformed our casino SEO. Our slot games now get indexed within hours instead of weeks, and our organic traffic has tripled.',
    author: {
      name: '<PERSON>',
      title: 'Head of Digital Marketing',
      company: 'Global Casino Network',
      imageUrl: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80',
    },
  },
  {
    body: 'The real-time optimization for our sports betting odds is incredible. We\'re now ranking for live events as they happen.',
    author: {
      name: '<PERSON>',
      title: 'SEO Director',
      company: 'Premier Sportsbook',
      imageUrl: 'https://images.unsplash.com/photo-1519244703995-f4e0f30006d5?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80',
    },
  },
  {
    body: 'Managing SEO for 12 different gaming brands was a nightmare. RankRender\'s centralized approach has been a game-changer.',
    author: {
      name: 'Sophie Chen',
      title: 'VP of Marketing',
      company: 'European Gaming Group',
      imageUrl: 'https://images.unsplash.com/photo-1517841905240-472988babdf9?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80',
    },
  },
]

export function SocialProof() {
  return (
    <div className="bg-white py-24 sm:py-32">
      <Container>
        {/* Stats Section */}
        <div className="mx-auto max-w-2xl lg:max-w-none">
          <div className="text-center">
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
              Trusted by leading iGaming operators
            </h2>
            <p className="mt-4 text-lg leading-8 text-gray-600">
              See why casino operators, sportsbooks, and poker platforms choose RankRender for their SEO optimization.
            </p>
          </div>
          <dl className="mt-16 grid grid-cols-1 gap-0.5 overflow-hidden rounded-2xl text-center sm:grid-cols-2 lg:grid-cols-4">
            {stats.map((stat) => (
              <div key={stat.id} className="flex flex-col bg-gray-400/5 p-8">
                <dt className="text-sm font-semibold leading-6 text-gray-600">{stat.name}</dt>
                <dd className="order-first text-3xl font-bold tracking-tight text-rankrender-600">
                  {stat.value}
                </dd>
              </div>
            ))}
          </dl>
        </div>

        {/* Testimonials Section */}
        <div className="mx-auto mt-32 max-w-2xl sm:mt-40 lg:max-w-none">
          <div className="text-center">
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
              What our clients say
            </h2>
            <p className="mt-4 text-lg leading-8 text-gray-600">
              Hear from iGaming operators who have transformed their SEO with RankRender.
            </p>
          </div>
          <div className="mx-auto mt-16 flow-root max-w-2xl sm:mt-20 lg:mx-0 lg:max-w-none">
            <div className="-mt-8 sm:-mx-4 sm:columns-1 sm:text-[0] lg:columns-3">
              {testimonials.map((testimonial, testimonialIdx) => (
                <div key={testimonialIdx} className="pt-8 sm:inline-block sm:w-full sm:px-4">
                  <figure className="rounded-2xl bg-gray-50 p-8 text-sm leading-6">
                    <blockquote className="text-gray-900">
                      <p>"{testimonial.body}"</p>
                    </blockquote>
                    <figcaption className="mt-6 flex items-center gap-x-4">
                      <img
                        className="h-10 w-10 rounded-full bg-gray-50"
                        src={testimonial.author.imageUrl}
                        alt=""
                      />
                      <div>
                        <div className="font-semibold text-gray-900">{testimonial.author.name}</div>
                        <div className="text-gray-600">
                          {testimonial.author.title}, {testimonial.author.company}
                        </div>
                      </div>
                    </figcaption>
                  </figure>
                </div>
              ))}
            </div>
          </div>
        </div>
      </Container>
    </div>
  )
}
