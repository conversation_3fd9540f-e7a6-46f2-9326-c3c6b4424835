@import 'tailwindcss';
@plugin '@tailwindcss/forms';

@theme {
  --text-*: initial;
  --text-xs: 0.75rem;
  --text-xs--line-height: 1rem;
  --text-sm: 0.875rem;
  --text-sm--line-height: 1.5rem;
  --text-base: 1rem;
  --text-base--line-height: 1.75rem;
  --text-lg: 1.125rem;
  --text-lg--line-height: 2rem;
  --text-xl: 1.25rem;
  --text-xl--line-height: 2rem;
  --text-2xl: 1.5rem;
  --text-2xl--line-height: 2rem;
  --text-3xl: 2rem;
  --text-3xl--line-height: 2.5rem;
  --text-4xl: 2.5rem;
  --text-4xl--line-height: 3.5rem;
  --text-5xl: 3rem;
  --text-5xl--line-height: 3.5rem;
  --text-6xl: 3.75rem;
  --text-6xl--line-height: 1;
  --text-7xl: 4.5rem;
  --text-7xl--line-height: 1.1;
  --text-8xl: 6rem;
  --text-8xl--line-height: 1;
  --text-9xl: 8rem;
  --text-9xl--line-height: 1;

  --radius-4xl: 2rem;

  --font-sans: var(--font-inter);
  --font-display: var(--font-lexend);

  --container-2xl: 40rem;

  --color-rankrender-50: #edfcf5;
  --color-rankrender-100: #d7f9e9;
  --color-rankrender-200: #b4f4d6;
  --color-rankrender-300: #8cedc0;
  --color-rankrender-400: #69e8ad;
  --color-rankrender-500: #41e296;
  --color-rankrender-600: #1fcc7b;
  --color-rankrender-700: #17965b;
  --color-rankrender-800: #10663e;
  --color-rankrender-900: #07311d;
  --color-rankrender-950: #041b10;

  --color-rankmain-50: #e5faff;
  --color-rankmain-100: #c7f5ff;
  --color-rankmain-200: #94ebff;
  --color-rankmain-300: #5ce1ff;
  --color-rankmain-400: #24d7ff;
  --color-rankmain-500: #00c4ee;
  --color-rankmain-600: #009abd;
  --color-rankmain-700: #00758f;
  --color-rankmain-800: #004f61;
  --color-rankmain-900: #00252e;
  --color-rankmain-950: #001519;

}

/* Mobile Navigation Animations */
@keyframes fade-in-up {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fade-in-up 0.4s ease-out forwards;
  opacity: 0;
}

/* Mobile Navigation Staggered Slide-in Animations */
@keyframes slide-in-stagger {
  0% {
    opacity: 0;
    transform: translateX(20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Staggered animations that trigger when panel is open */
[data-headlessui-state="open"] .animate-slide-in-stagger-1 {
  animation: slide-in-stagger 0.4s ease-out 0.1s forwards;
}

[data-headlessui-state="open"] .animate-slide-in-stagger-2 {
  animation: slide-in-stagger 0.4s ease-out 0.2s forwards;
}

[data-headlessui-state="open"] .animate-slide-in-stagger-3 {
  animation: slide-in-stagger 0.4s ease-out 0.3s forwards;
}

[data-headlessui-state="open"] .animate-slide-in-stagger-4 {
  animation: slide-in-stagger 0.4s ease-out 0.4s forwards;
}

[data-headlessui-state="open"] .animate-slide-in-stagger-5 {
  animation: slide-in-stagger 0.4s ease-out 0.5s forwards;
}

/* Initial state for staggered elements */
.animate-slide-in-stagger-1,
.animate-slide-in-stagger-2,
.animate-slide-in-stagger-3,
.animate-slide-in-stagger-4,
.animate-slide-in-stagger-5 {
  opacity: 0;
  transform: translateX(20px);
}
